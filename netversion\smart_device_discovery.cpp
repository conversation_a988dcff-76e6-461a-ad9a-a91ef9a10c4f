#include "dma_network_protocol.h"
#include <setupapi.h>
#include <iostream>
#include <vector>
#include <windows.h>
#include <winternl.h>
#include <devguid.h>

#pragma comment(lib, "setupapi.lib")
#pragma comment(lib, "ntdll.lib")

// NT API定义
typedef struct _UNICODE_STRING {
    USHORT Length;
    USHORT MaximumLength;
    PWSTR  Buffer;
} UNICODE_STRING, *PUNICODE_STRING;

typedef struct _OBJECT_ATTRIBUTES {
    ULONG Length;
    HANDLE RootDirectory;
    PUNICODE_STRING ObjectName;
    ULONG Attributes;
    PVOID SecurityDescriptor;
    PVOID SecurityQualityOfService;
} OBJECT_ATTRIBUTES, *POBJECT_ATTRIBUTES;

typedef struct _OBJECT_DIRECTORY_INFORMATION {
    UNICODE_STRING Name;
    UNICODE_STRING TypeName;
} OBJECT_DIRECTORY_INFORMATION, *POBJECT_DIRECTORY_INFORMATION;

#define InitializeObjectAttributes(p, n, a, r, s) { \
    (p)->Length = sizeof(OBJECT_ATTRIBUTES);          \
    (p)->RootDirectory = r;                             \
    (p)->Attributes = a;                                \
    (p)->ObjectName = n;                                \
    (p)->SecurityDescriptor = s;                        \
    (p)->SecurityQualityOfService = NULL;               \
}

#define OBJ_CASE_INSENSITIVE    0x00000040L

extern "C" {
    NTSTATUS NTAPI NtOpenDirectoryObject(
        OUT PHANDLE DirectoryHandle,
        IN ACCESS_MASK DesiredAccess,
        IN POBJECT_ATTRIBUTES ObjectAttributes
    );

    NTSTATUS NTAPI NtQueryDirectoryObject(
        IN HANDLE DirectoryHandle,
        OUT PVOID Buffer,
        IN ULONG Length,
        IN BOOLEAN ReturnSingleEntry,
        IN BOOLEAN RestartScan,
        IN OUT PULONG Context,
        OUT PULONG ReturnLength OPTIONAL
    );

    NTSTATUS NTAPI RtlInitUnicodeString(
        PUNICODE_STRING DestinationString,
        PCWSTR SourceString
    );
}

// 智能设备发现 - 通过NT对象目录枚举
BOOL DmaDevice_SmartDiscovery(LPWSTR devicePath, DWORD pathSize)
{
    if (!devicePath || pathSize < MAX_PATH) {
        return FALSE;
    }

    ZeroMemory(devicePath, pathSize * sizeof(WCHAR));

    // 打开\Device目录
    UNICODE_STRING objectName;
    RtlInitUnicodeString(&objectName, L"\\Device");
    
    OBJECT_ATTRIBUTES objectAttributes;
    InitializeObjectAttributes(&objectAttributes, &objectName, OBJ_CASE_INSENSITIVE, NULL, NULL);
    
    HANDLE directoryHandle;
    NTSTATUS status = NtOpenDirectoryObject(&directoryHandle, DIRECTORY_QUERY, &objectAttributes);
    
    if (!NT_SUCCESS(status)) {
        printf("[!] Failed to open \\Device directory: 0x%08X\n", status);
        return FALSE;
    }

    // 分配缓冲区用于目录查询
    ULONG bufferSize = 4096;
    PVOID buffer = malloc(bufferSize);
    if (!buffer) {
        CloseHandle(directoryHandle);
        return FALSE;
    }

    ULONG context = 0;
    BOOLEAN restartScan = TRUE;
    
    printf("[*] Enumerating \\Device directory for GUID-like devices...\n");

    while (TRUE) {
        ULONG returnLength;
        status = NtQueryDirectoryObject(
            directoryHandle,
            buffer,
            bufferSize,
            TRUE,  // 返回单个条目
            restartScan,
            &context,
            &returnLength
        );

        if (status == STATUS_NO_MORE_ENTRIES) {
            break;
        }

        if (!NT_SUCCESS(status)) {
            printf("[!] Directory query failed: 0x%08X\n", status);
            break;
        }

        restartScan = FALSE;

        POBJECT_DIRECTORY_INFORMATION dirInfo = (POBJECT_DIRECTORY_INFORMATION)buffer;
        if (dirInfo->Name.Buffer && dirInfo->Name.Length > 0) {
            // 检查是否是GUID格式的设备名
            WCHAR* deviceName = dirInfo->Name.Buffer;
            int nameLen = dirInfo->Name.Length / sizeof(WCHAR);
            
            // GUID格式: {xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx}
            if (nameLen >= 36 && deviceName[0] == L'{' && deviceName[nameLen-1] == L'}') {
                // 构建完整的设备路径
                WCHAR fullDevicePath[MAX_PATH];
                swprintf_s(fullDevicePath, MAX_PATH, L"\\\\.\\%.*ws", nameLen, deviceName);
                
                printf("[*] Testing GUID device: %ws\n", fullDevicePath);
                
                // 尝试打开设备
                HANDLE hDevice = CreateFileW(
                    fullDevicePath,
                    GENERIC_READ | GENERIC_WRITE,
                    FILE_SHARE_READ | FILE_SHARE_WRITE,
                    NULL,
                    OPEN_EXISTING,
                    FILE_ATTRIBUTE_NORMAL,
                    NULL
                );

                if (hDevice != INVALID_HANDLE_VALUE) {
                    // 测试设备识别
                    DWORD signature = 0;
                    DWORD bytesReturned = 0;
                    
                    BOOL result = DeviceIoControl(
                        hDevice,
                        IOCTL_DMA_IDENTIFY,
                        NULL, 0,
                        &signature, sizeof(signature),
                        &bytesReturned,
                        NULL
                    );

                    if (result && signature == DMA_DEVICE_SIGNATURE) {
                        printf("[+] Found DMA device: %ws\n", fullDevicePath);
                        wcscpy_s(devicePath, pathSize, fullDevicePath);
                        CloseHandle(hDevice);
                        free(buffer);
                        CloseHandle(directoryHandle);
                        return TRUE;
                    }
                    
                    CloseHandle(hDevice);
                }
            }
        }
    }

    free(buffer);
    CloseHandle(directoryHandle);
    printf("[!] DMA device not found through NT object directory enumeration\n");
    return FALSE;
}

// 回退到暴力搜索方式
BOOL DmaDevice_BruteForceSearch(LPWSTR devicePath, DWORD pathSize)
{
    printf("[*] Falling back to brute force search...\n");
    
    // 尝试一些常见的GUID模式
    const WCHAR* patterns[] = {
        L"\\\\.\\{%08x-%04x-%04x-%04x-%08x%04x}",
        L"\\\\.\\{%08X-%04X-%04X-%04X-%08X%04X}",
    };

    for (int pattern = 0; pattern < 2; pattern++) {
        for (int base = 0; base < 10; base++) {
            for (int i = 0; i < 1000; i++) {
                WCHAR testPath[MAX_PATH];
                
                ULONG seed = GetTickCount() + base * 1000 + i;
                ULONG part1 = seed;
                ULONG part2 = seed ^ 0x12345678;
                ULONG part3 = seed ^ 0x87654321;
                ULONG part4 = seed ^ 0xABCDEF00;
                
                swprintf_s(testPath, MAX_PATH, patterns[pattern],
                    part1,
                    (USHORT)(part2 >> 16),
                    (USHORT)(part2 & 0xFFFF),
                    (USHORT)(part3 >> 16),
                    part4,
                    (USHORT)(part3 & 0xFFFF));

                HANDLE hDevice = CreateFileW(
                    testPath,
                    GENERIC_READ | GENERIC_WRITE,
                    FILE_SHARE_READ | FILE_SHARE_WRITE,
                    NULL,
                    OPEN_EXISTING,
                    FILE_ATTRIBUTE_NORMAL,
                    NULL
                );

                if (hDevice != INVALID_HANDLE_VALUE) {
                    DWORD signature = 0;
                    DWORD bytesReturned = 0;
                    
                    BOOL result = DeviceIoControl(
                        hDevice,
                        IOCTL_DMA_IDENTIFY,
                        NULL, 0,
                        &signature, sizeof(signature),
                        &bytesReturned,
                        NULL
                    );

                    if (result && signature == DMA_DEVICE_SIGNATURE) {
                        printf("[+] Found DMA device: %ws\n", testPath);
                        wcscpy_s(devicePath, pathSize, testPath);
                        CloseHandle(hDevice);
                        return TRUE;
                    }
                    
                    CloseHandle(hDevice);
                }
            }
        }
    }

    return FALSE;
}

// 改进的设备发现函数
BOOL DmaDevice_FindRandomDeviceImproved(LPWSTR devicePath, DWORD pathSize)
{
    if (!devicePath || pathSize < MAX_PATH) {
        return FALSE;
    }

    printf("[*] Starting smart DMA device discovery...\n");

    // 首先尝试智能发现
    if (DmaDevice_SmartDiscovery(devicePath, pathSize)) {
        return TRUE;
    }

    // 如果智能发现失败，回退到暴力搜索
    return DmaDevice_BruteForceSearch(devicePath, pathSize);
}