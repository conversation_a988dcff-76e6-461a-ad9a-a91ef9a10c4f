#include <windows.h>
#include <iostream>
#include "dma_network_protocol.h"

int main()
{
    printf("DMA Device Discovery Test\n");
    printf("=========================\n");

    WCHAR devicePath[MAX_PATH];
    
    // 查找随机GUID设备
    printf("[*] Searching for DMA device with random GUID name...\n");
    
    if (DmaDevice_FindRandomDevice(devicePath, MAX_PATH)) {
        printf("[+] Found device: %ws\n", devicePath);
        
        // 尝试打开设备
        HANDLE hDevice = DmaDevice_OpenByPath(devicePath);
        if (hDevice != INVALID_HANDLE_VALUE) {
            printf("[+] Successfully opened device handle\n");
            
            // 测试设备通信
            DWORD signature = 0;
            DWORD bytesReturned = 0;
            
            BOOL result = DeviceIoControl(
                hDevice,
                IOCTL_DMA_IDENTIFY,
                NULL, 0,
                &signature, sizeof(signature),
                &bytesReturned,
                NULL
            );
            
            if (result && signature == DMA_DEVICE_SIGNATURE) {
                printf("[+] Device signature verified: 0x%08X\n", signature);
                printf("[+] Device is ready for DMA operations\n");
            } else {
                printf("[!] Device signature verification failed\n");
            }
            
            CloseHandle(hDevice);
        } else {
            printf("[!] Failed to open device\n");
        }
    } else {
        printf("[!] DMA device not found\n");
        printf("[*] Make sure the ReadPhys driver is loaded\n");
    }

    printf("\nPress any key to continue...\n");
    getchar();
    return 0;
}