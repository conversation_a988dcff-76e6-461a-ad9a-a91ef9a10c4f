#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <stdio.h>
#include <signal.h>
#include "dma_network_protocol.h"

// �źŴ���
static volatile BOOL g_shutdown = FALSE;

void SignalHandler(int signal) {
    printf("\\n�յ�ֹͣ�źţ����ڹرշ�����...\\n");
    g_shutdown = TRUE;
}

int main(int argc, char* argv[]) {
    WORD port = DMA_SERVER_PORT;
    
    printf("=== DMA��������� ===\\n");
    printf("��;: ��������DMA����ת�����ں�����\\n");
    printf("�˿�: %d\\n\\n", port);
    
    // �����źŴ���
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);
    
    // ���������в���
    if (argc > 1) {
        port = (WORD)atoi(argv[1]);
        if (port == 0) {
            printf("��Ч�˿ں�: %s\\n", argv[1]);
            return 1;
        }
        printf("ʹ���Զ���˿�: %d\\n", port);
    }
    
    // ��ʼ��������
    if (!DmaServer_Initialize(port)) {
        printf("��������ʼ��ʧ��\\n");
        return 1;
    }
    
    printf("\\n��Ҫ��ʾ:\\n");
    printf("1. ȷ���ں���������ȷ����\\n");
    printf("2. ������Ҫ����ԱȨ������\\n");
    printf("3. ��Ctrl+Cֹͣ������\\n\\n");
    
    // �����������������������ã�
    DmaServer_Start();
    
    // ������Դ
    DmaServer_Stop();
    
    printf("�������������˳�\\n");
    return 0;
}