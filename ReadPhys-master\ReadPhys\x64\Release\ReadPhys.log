﻿  Building 'ReadPhys' with toolset 'OLLVM-MSVC_KernelMode' and the 'Desktop' target platform.
  llvm-msvc(v777.2.2) spent 2.506s in entry.cpp
  could not open '/DISCARD:.pdata': no such file or directory
  ReadPhys.vcxproj -> C:\Users\<USER>\Desktop\请勿下载到游戏机\副机\newleechcore\ReadPhys-master\x64\Release\ReadPhys.sys
  Inf2Cat task was skipped as there were no inf files to process
  
  DrvCat task was skipped as there was no catalog file to process
  
