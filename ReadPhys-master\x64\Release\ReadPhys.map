 ReadPhys

 Timestamp is 688e7ab1 (Sun Aug  3 04:53:05 2025)

 Preferred load address is 0000000140000000

 Start         Length     Name                   Class
 0001:00000000 000734f1H .text                   CODE
 0001:00073500 000000d0H .text$mn                CODE
 0001:000735d0 00000036H .text$mn$00             CODE
 0001:00073640 000004c0H .text$mn$21             CODE
 0001:00073b00 00000180H .00cfg                  CODE
 0001:00073c80 000001d4H .pdata                  CODE
 0001:00073e60 00000148H .rdata                  CODE
 0001:00073fa8 00000240H .xdata                  CODE
 0002:00000000 0000fb90H .data                   DATA
 0002:0000fb90 00000240H .bss                    DATA

  Address         Publics by Value              Rva+Base               Lib:Object

 0000:00000000       __guard_fids_table         0000000000000000     <absolute>
 0000:00000000       __guard_fids_count         0000000000000000     <absolute>
 0000:00000000       __guard_flags              0000000000000000     <absolute>
 0000:00000000       __guard_iat_table          0000000000000000     <absolute>
 0000:00000000       __guard_iat_count          0000000000000000     <absolute>
 0000:00000000       __guard_longjmp_table      0000000000000000     <absolute>
 0000:00000000       __guard_longjmp_count      0000000000000000     <absolute>
 0000:00000000       __guard_eh_cont_table      0000000000000000     <absolute>
 0000:00000000       __guard_eh_cont_count      0000000000000000     <absolute>
 0001:00000000       ?InitializeKliCache@Anti4heatExpert@@YAXXZ 0000000140001000 f   Anti4heatExpert.obj
 0001:00031d82       ?RaiseIRQL@Anti4heatExpert@@YAEE@Z 0000000140032d82 f   Anti4heatExpert.obj
 0001:00031d9c       ?RaiseIrqlToDpcLv@Anti4heatExpert@@YAEXZ 0000000140032d9c f   Anti4heatExpert.obj
 0001:00031dcf       ?LowerIrql@Anti4heatExpert@@YAXE@Z 0000000140032dcf f   Anti4heatExpert.obj
 0001:00031dd7       ?IsPhysPageInRange@Anti4heatExpert@@YA_N_K0@Z 0000000140032dd7 f   Anti4heatExpert.obj
 0001:00032382       ?IsVaPhysicalAddressValid@Anti4heatExpert@@YA_NPEAX@Z 0000000140033382 f   Anti4heatExpert.obj
 0001:000323cb       ?GetPML4Base@Anti4heatExpert@@YAPEAXT_LARGE_INTEGER@@@Z 00000001400333cb f   Anti4heatExpert.obj
 0001:00032480       ?InitializePteBase@Anti4heatExpert@@YAKXZ 0000000140033480 f   Anti4heatExpert.obj
 0001:00032a7a       ?GetPteAddress@Anti4heatExpert@@YA_KPEAX@Z 0000000140033a7a f   Anti4heatExpert.obj
 0001:00032ac7       ?AllocatePhysicalPage@Anti4heatExpert@@YAKPEAUPHYSICAL_PAGE_INFO@1@_K@Z 0000000140033ac7 f   Anti4heatExpert.obj
 0001:000331db       ?FreePhysicalPage@Anti4heatExpert@@YAXPEAUPHYSICAL_PAGE_INFO@1@@Z 00000001400341db f   Anti4heatExpert.obj
 0001:000332f0       ?ReadPhysicalPage@Anti4heatExpert@@YAKPEAUPHYSICAL_PAGE_INFO@1@_KPEAX1@Z 00000001400342f0 f   Anti4heatExpert.obj
 0001:00033c1f       ?GetPageTableInfo@Anti4heatExpert@@YAKPEAUPHYSICAL_PAGE_INFO@1@_K1PEAUPAGE_TABLE_INFO@1@@Z 0000000140034c1f f   Anti4heatExpert.obj
 0001:00034766       ?GetPhysPageAddress@Anti4heatExpert@@YAKPEAUPHYSICAL_PAGE_INFO@1@_KPEAXPEA_K@Z 0000000140035766 f   Anti4heatExpert.obj
 0001:00034f59       ?GetPhysPageSize@Anti4heatExpert@@YAKPEAUPHYSICAL_PAGE_INFO@1@_KPEA_K1@Z 0000000140035f59 f   Anti4heatExpert.obj
 0001:00037bad       ?get_kernel_base@detail@kli@@YA_KXZ 0000000140038bad f   Anti4heatExpert.obj
 0001:00037c2c       GUID_ECP_FLT_CREATEFILE_TARGET 0000000140038c2c f   Anti4heatExpert.obj
 0001:00037c40       __chkstk                   0000000140038c40 f   entry.obj
 0001:00037c41       ?DeobfuscateString@@YAXPEA_WPEBEKE@Z 0000000140038c41 f   entry.obj
 0001:00037d46       ?SimpleHash@@YAKPEBDK@Z    0000000140038d46 f   entry.obj
 0001:00037e08       ?GeneratePredictableGUID@@YAXPEAK000@Z 0000000140038e08 f   entry.obj
 0001:000382d8       ?GenerateRandomDeviceName@@YAJPEAU_UNICODE_STRING@@0@Z 00000001400392d8 f   entry.obj
 0001:00038d1e       ?ReadVirtualMemory@@YAK_KPEAX1PEAK@Z 0000000140039d1e f   entry.obj
 0001:00039ff2       ?ReadPhysicalMemory@@YAK_KPEAXKPEAK@Z 000000014003aff2 f   entry.obj
 0001:0003a8f9       ?DmaDeviceControl@@YAJPEAU_DEVICE_OBJECT@@PEAU_IRP@@@Z 000000014003b8f9 f   entry.obj
 0001:0003b0d3       ?DmaDispatchCreateClose@@YAJPEAU_DEVICE_OBJECT@@PEAU_IRP@@@Z 000000014003c0d3 f   entry.obj
 0001:0003b16b       DriverEntry                000000014003c16b f   entry.obj
 0001:00073500       __cpu_features_init        0000000140074500 f   ntoskrnl:cpuinfo.obj
 0001:000735c0       _guard_check_icall_nop     00000001400745c0 f   ntoskrnl:guard_check.obj
 0001:000735e0       _guard_dispatch_icall_nop  00000001400745e0 f   ntoskrnl:guard_dispatch.obj
 0001:00073600       _guard_xfg_dispatch_icall_nop 0000000140074600 f   ntoskrnl:guard_xfg_dispatch.obj
 0001:00073640       memmove                    0000000140074640 f   ntoskrnl:memcpy.obj
 0001:00073640       memcpy                     0000000140074640 f   ntoskrnl:memcpy.obj
 0001:00073900       memset                     0000000140074900 f   ntoskrnl:memset.obj
 0001:00073900       memset_$fo_default$        0000000140074900 f   ntoskrnl:memset.obj
 0001:00073a40       __memset_repmovs           0000000140074a40 f   ntoskrnl:memset.obj
 0001:00073ac0       __memset_query             0000000140074ac0 f   ntoskrnl:memset.obj
 0001:00073b00       __uma_functions            0000000140074b00     BufferOverflowFastFailK:loadcfg.obj
 0001:00073c50       __guard_check_icall_fptr   0000000140074c50     ntoskrnl:guard_support.obj
 0001:00073c58       __guard_dispatch_icall_fptr 0000000140074c58     ntoskrnl:guard_support.obj
 0001:00073c60       __guard_xfg_check_icall_fptr 0000000140074c60     ntoskrnl:guard_support.obj
 0001:00073c68       __guard_xfg_dispatch_icall_fptr 0000000140074c68     ntoskrnl:guard_support.obj
 0001:00073c70       __guard_xfg_table_dispatch_icall_fptr 0000000140074c70     ntoskrnl:guard_support.obj
 0001:00073c78       __castguard_check_failure_os_handled_fptr 0000000140074c78     ntoskrnl:guard_support.obj
 0001:00073e60       _load_config_used          0000000140074e60     BufferOverflowFastFailK:loadcfg.obj
 0002:00007c40       ?base@cache@kli@@3_KA      000000014007dc40     Anti4heatExpert.obj
 0002:0000fb40       __isa_info                 0000000140085b40     ntoskrnl:cpuinfo.obj
 0002:0000fb80       __security_cookie          0000000140085b80     BufferOverflowFastFailK:gs_support.obj
 0002:0000fb90       ?g_PteBase@Anti4heatExpert@@3_KA 0000000140085b90     Anti4heatExpert.obj
 0002:0000fb98       ?g_PdeBase@Anti4heatExpert@@3_KA 0000000140085b98     Anti4heatExpert.obj
 0002:0000fba0       ?g_PpeBase@Anti4heatExpert@@3_KA 0000000140085ba0     Anti4heatExpert.obj
 0002:0000fba8       ?g_PxeBase@Anti4heatExpert@@3_KA 0000000140085ba8     Anti4heatExpert.obj
 0002:0000fbb0       ?g_IsInitPteBaseForSystem@Anti4heatExpert@@3EA 0000000140085bb0     Anti4heatExpert.obj
 0002:0000fbb8       ?g_PhysicalMemoryRanges@Anti4heatExpert@@3PEAU_PHYSICAL_MEMORY_RANGE@@EA 0000000140085bb8     Anti4heatExpert.obj
 0002:0000fbc0       ?KLIKeGetCurrentIrql@@3P6AEXZEA 0000000140085bc0     Anti4heatExpert.obj
 0002:0000fbc8       ?KLIMmGetPhysicalMemoryRanges@@3P6APEAU_PHYSICAL_MEMORY_RANGE@@XZEA 0000000140085bc8     Anti4heatExpert.obj
 0002:0000fbd0       ?KLIMmGetPhysicalAddress@@3P6A?AT_LARGE_INTEGER@@PEAX@ZEA 0000000140085bd0     Anti4heatExpert.obj
 0002:0000fbd8       ?KLIMmGetVirtualForPhysical@@3P6APEAXT_LARGE_INTEGER@@@ZEA 0000000140085bd8     Anti4heatExpert.obj
 0002:0000fbe0       ?KLIMmAllocateMappingAddress@@3P6APEAX_KK@ZEA 0000000140085be0     Anti4heatExpert.obj
 0002:0000fbe8       ?KLIMmFreeMappingAddress@@3P6AXPEAXK@ZEA 0000000140085be8     Anti4heatExpert.obj
 0002:0000fbf0       ?KLIIoCreateDevice@@3P6AJPEAU_DRIVER_OBJECT@@KPEAU_UNICODE_STRING@@KKEPEAPEAU_DEVICE_OBJECT@@@ZEA 0000000140085bf0     Anti4heatExpert.obj
 0002:0000fbf8       ?KLIIoCreateSymbolicLink@@3P6AJPEAU_UNICODE_STRING@@0@ZEA 0000000140085bf8     Anti4heatExpert.obj
 0002:0000fc00       ?KLIIoDeleteDevice@@3P6AXPEAU_DEVICE_OBJECT@@@ZEA 0000000140085c00     Anti4heatExpert.obj
 0002:0000fc08       ?KLIIoDeleteSymbolicLink@@3P6AJPEAU_UNICODE_STRING@@@ZEA 0000000140085c08     Anti4heatExpert.obj
 0002:0000fc10       ?KLIKeQueryTimeIncrement@@3P6AKXZEA 0000000140085c10     Anti4heatExpert.obj
 0002:0000fc18       _fltused                   0000000140085c18     entry.obj
 0002:0000fc20       ?g_DeviceName@@3U_UNICODE_STRING@@A 0000000140085c20     entry.obj
 0002:0000fc30       ?g_DosDeviceName@@3U_UNICODE_STRING@@A 0000000140085c30     entry.obj
 0002:0000fda0       ?KLIExAllocatePool2@@3P6APEAX_K0K@ZEA 0000000140085da0     entry.obj
 0002:0000fda8       ?KLIExFreePool@@3P6AXPEAX@ZEA 0000000140085da8     entry.obj
 0002:0000fdb0       ?KLIRtlStringCchPrintfW@@3P6AJPEA_W_KPEB_WZZEA 0000000140085db0     entry.obj
 0002:0000fdb8       ?KLIRtlInitUnicodeString@@3P6AXPEAU_UNICODE_STRING@@PEB_W@ZEA 0000000140085db8     entry.obj
 0002:0000fdc0       ?KLIIofCompleteRequest@@3P6AXPEAU_IRP@@D@ZEA 0000000140085dc0     entry.obj
 0002:0000fdc8       ?KLIDbgPrint@@3P6AKPEBDZZEA 0000000140085dc8     entry.obj

 entry point at         0001:0003b16b

 Static symbols

 0000:00000000       __guard_fids__             0000000140000000     ntoskrnl:guard_support.obj
 0000:00000000       __guard_fids__             0000000140000000     ntoskrnl:guard_support.obj
 0001:000725a6       ?__invoke@<lambda_0>@?0??DriverEntry@@9@CA@PEAU_DRIVER_OBJECT@@@Z 00000001400735a6 f   entry.obj
 0001:00072760       .Lgoron_decrypt_string_0   0000000140073760 f   entry.obj
 0001:00072850       .Lgoron_decrypt_string_1   0000000140073850 f   entry.obj
 0001:00072960       .Lgoron_decrypt_string_2   0000000140073960 f   entry.obj
 0001:00072a40       .Lgoron_decrypt_string_3   0000000140073a40 f   entry.obj
 0001:00072b40       .Lgoron_decrypt_string_4   0000000140073b40 f   entry.obj
 0001:00072c40       .Lgoron_decrypt_string_5   0000000140073c40 f   entry.obj
 0001:00072d40       .Lgoron_decrypt_string_6   0000000140073d40 f   entry.obj
 0001:00072e30       .Lgoron_decrypt_string_7   0000000140073e30 f   entry.obj
 0001:00072f20       .Lgoron_decrypt_string_8   0000000140073f20 f   entry.obj
 0001:00073020       .Lgoron_decrypt_string_9   0000000140074020 f   entry.obj
 0001:00073120       g_DeviceFormat             0000000140074120 f   entry.obj
 0001:00073170       g_DosFormat                0000000140074170 f   entry.obj
 0001:000735e0       $$000000                   00000001400745e0     ntoskrnl:guard_dispatch.obj
 0001:00073600       $$000000                   0000000140074600     ntoskrnl:guard_xfg_dispatch.obj
 0001:00073640       $$000000                   0000000140074640     ntoskrnl:memcpy.obj
 0001:00073660       mcpy00                     0000000140074660 f   ntoskrnl:memcpy.obj
 0001:00073680       mcpy10                     0000000140074680 f   ntoskrnl:memcpy.obj
 0001:0007369a       mcpy20                     000000014007469a f   ntoskrnl:memcpy.obj
 0001:000736a0       mcpy30                     00000001400746a0 f   ntoskrnl:memcpy.obj
 0001:000736c0       xcpy00                     00000001400746c0 f   ntoskrnl:memcpy.obj
 0001:000736f1       xcpy10                     00000001400746f1 f   ntoskrnl:memcpy.obj
 0001:00073740       xcpy13                     0000000140074740 f   ntoskrnl:memcpy.obj
 0001:0007376f       xcpy15                     000000014007476f f   ntoskrnl:memcpy.obj
 0001:00073791       xcpy20                     0000000140074791 f   ntoskrnl:memcpy.obj
 0001:000737a5       xcpy30                     00000001400747a5 f   ntoskrnl:memcpy.obj
 0001:000737c0       lcpy00                     00000001400747c0 f   ntoskrnl:memcpy.obj
 0001:00073800       lcpy10                     0000000140074800 f   ntoskrnl:memcpy.obj
 0001:00073840       xmov00                     0000000140074840 f   ntoskrnl:memcpy.obj
 0001:0007386d       xmov10                     000000014007486d f   ntoskrnl:memcpy.obj
 0001:00073880       xmov13                     0000000140074880 f   ntoskrnl:memcpy.obj
 0001:000738af       xmov15                     00000001400748af f   ntoskrnl:memcpy.obj
 0001:000738d1       xmov20                     00000001400748d1 f   ntoskrnl:memcpy.obj
 0001:000738e6       xmov30                     00000001400748e6 f   ntoskrnl:memcpy.obj
 0001:00073900       $$000000                   0000000140074900     ntoskrnl:memset.obj
 0001:00073922       mset10                     0000000140074922 f   ntoskrnl:memset.obj
 0001:00073938       mset20                     0000000140074938 f   ntoskrnl:memset.obj
 0001:00073961       mset30                     0000000140074961 f   ntoskrnl:memset.obj
 0001:00073990       mset45                     0000000140074990 f   ntoskrnl:memset.obj
 0001:00073996       mset46                     0000000140074996 f   ntoskrnl:memset.obj
 0001:000739c0       mset47                     00000001400749c0 f   ntoskrnl:memset.obj
 0001:000739f0       mset48                     00000001400749f0 f   ntoskrnl:memset.obj
 0001:00073a06       mset50                     0000000140074a06 f   ntoskrnl:memset.obj
 0001:00073a40       $$000000                   0000000140074a40     ntoskrnl:memset.obj
 0001:00073a4a       repmovs10                  0000000140074a4a f   ntoskrnl:memset.obj
 0001:00073a7c       repmovs20                  0000000140074a7c f   ntoskrnl:memset.obj
 0001:00073ac0       $$000000                   0000000140074ac0     ntoskrnl:memset.obj
 0001:00073e30       $pdata$__cpu_features_init 0000000140074e30     ntoskrnl:cpuinfo.obj
 0001:000741b0       $xdatasym$memcpy           00000001400751b0     ntoskrnl:memcpy.obj
 0001:000741b0       $xdatasym                  00000001400751b0     ntoskrnl:memcpy.obj
 0001:000741b0       $xdatasym$memset           00000001400751b0     ntoskrnl:memset.obj
 0001:000741b0       $xdatasym$_guard_dispatch_icall_nop 00000001400751b0     ntoskrnl:guard_dispatch.obj
 0001:000741b0       $xdatasym$_guard_xfg_dispatch_icall_nop 00000001400751b0     ntoskrnl:guard_xfg_dispatch.obj
 0001:000741b8       $xdatasym$__memset_repmovs 00000001400751b8     ntoskrnl:memset.obj
 0001:000741b8       $xdatasym                  00000001400751b8     ntoskrnl:memset.obj
 0001:000741c8       $xdatasym$__memset_query   00000001400751c8     ntoskrnl:memset.obj
 0001:000741dc       $unwind$__cpu_features_init 00000001400751dc     ntoskrnl:cpuinfo.obj
 0001:000741e8       $xdatasym                  00000001400751e8     ntoskrnl:memset_spec_ermsb.obj
 0001:000741e8       $xdatasym                  00000001400751e8     ntoskrnl:memset_spec_plain.obj
 0001:000741e8       $xdatasym                  00000001400751e8     ntoskrnl:guard_dispatch.obj
 0001:000741e8       $xdatasym                  00000001400751e8     ntoskrnl:guard_xfg_dispatch.obj
