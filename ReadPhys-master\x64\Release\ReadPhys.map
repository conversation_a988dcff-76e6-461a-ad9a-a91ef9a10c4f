 ReadPhys

 Timestamp is 688e7605 (Sun Aug  3 04:33:09 2025)

 Preferred load address is 0000000140000000

 Start         Length     Name                   Class
 0001:00000000 00072a85H .text                   CODE
 0001:00072a90 000000d0H .text$mn                CODE
 0001:00072b60 00000036H .text$mn$00             CODE
 0001:00072bc0 000004c0H .text$mn$21             CODE
 0001:00073080 00000180H .00cfg                  CODE
 0001:00073200 000001d4H .pdata                  CODE
 0001:000733e0 00000148H .rdata                  CODE
 0001:00073528 00000238H .xdata                  CODE
 0002:00000000 0000fb50H .data                   DATA
 0002:0000fb50 00000238H .bss                    DATA

  Address         Publics by Value              Rva+Base               Lib:Object

 0000:00000000       __guard_fids_table         0000000000000000     <absolute>
 0000:00000000       __guard_fids_count         0000000000000000     <absolute>
 0000:00000000       __guard_flags              0000000000000000     <absolute>
 0000:00000000       __guard_iat_table          0000000000000000     <absolute>
 0000:00000000       __guard_iat_count          0000000000000000     <absolute>
 0000:00000000       __guard_longjmp_table      0000000000000000     <absolute>
 0000:00000000       __guard_longjmp_count      0000000000000000     <absolute>
 0000:00000000       __guard_eh_cont_table      0000000000000000     <absolute>
 0000:00000000       __guard_eh_cont_count      0000000000000000     <absolute>
 0001:00000000       ?InitializeKliCache@Anti4heatExpert@@YAXXZ 0000000140001000 f   Anti4heatExpert.obj
 0001:00031d82       ?RaiseIRQL@Anti4heatExpert@@YAEE@Z 0000000140032d82 f   Anti4heatExpert.obj
 0001:00031d9c       ?RaiseIrqlToDpcLv@Anti4heatExpert@@YAEXZ 0000000140032d9c f   Anti4heatExpert.obj
 0001:00031dcf       ?LowerIrql@Anti4heatExpert@@YAXE@Z 0000000140032dcf f   Anti4heatExpert.obj
 0001:00031dd7       ?IsPhysPageInRange@Anti4heatExpert@@YA_N_K0@Z 0000000140032dd7 f   Anti4heatExpert.obj
 0001:00032382       ?IsVaPhysicalAddressValid@Anti4heatExpert@@YA_NPEAX@Z 0000000140033382 f   Anti4heatExpert.obj
 0001:000323cb       ?GetPML4Base@Anti4heatExpert@@YAPEAXT_LARGE_INTEGER@@@Z 00000001400333cb f   Anti4heatExpert.obj
 0001:00032480       ?InitializePteBase@Anti4heatExpert@@YAKXZ 0000000140033480 f   Anti4heatExpert.obj
 0001:00032a7a       ?GetPteAddress@Anti4heatExpert@@YA_KPEAX@Z 0000000140033a7a f   Anti4heatExpert.obj
 0001:00032ac7       ?AllocatePhysicalPage@Anti4heatExpert@@YAKPEAUPHYSICAL_PAGE_INFO@1@_K@Z 0000000140033ac7 f   Anti4heatExpert.obj
 0001:000331db       ?FreePhysicalPage@Anti4heatExpert@@YAXPEAUPHYSICAL_PAGE_INFO@1@@Z 00000001400341db f   Anti4heatExpert.obj
 0001:000332f0       ?ReadPhysicalPage@Anti4heatExpert@@YAKPEAUPHYSICAL_PAGE_INFO@1@_KPEAX1@Z 00000001400342f0 f   Anti4heatExpert.obj
 0001:00033c1f       ?GetPageTableInfo@Anti4heatExpert@@YAKPEAUPHYSICAL_PAGE_INFO@1@_K1PEAUPAGE_TABLE_INFO@1@@Z 0000000140034c1f f   Anti4heatExpert.obj
 0001:00034766       ?GetPhysPageAddress@Anti4heatExpert@@YAKPEAUPHYSICAL_PAGE_INFO@1@_KPEAXPEA_K@Z 0000000140035766 f   Anti4heatExpert.obj
 0001:00034f59       ?GetPhysPageSize@Anti4heatExpert@@YAKPEAUPHYSICAL_PAGE_INFO@1@_KPEA_K1@Z 0000000140035f59 f   Anti4heatExpert.obj
 0001:00037bad       ?get_kernel_base@detail@kli@@YA_KXZ 0000000140038bad f   Anti4heatExpert.obj
 0001:00037c2c       GUID_ECP_FLT_CREATEFILE_TARGET 0000000140038c2c f   Anti4heatExpert.obj
 0001:00037c40       __chkstk                   0000000140038c40 f   entry.obj
 0001:00037c41       ?DeobfuscateString@@YAXPEA_WPEBEKE@Z 0000000140038c41 f   entry.obj
 0001:00037d51       ?SimpleHash@@YAKPEBDK@Z    0000000140038d51 f   entry.obj
 0001:00037e15       ?GeneratePredictableGUID@@YAXPEAK000@Z 0000000140038e15 f   entry.obj
 0001:000381ee       ?GenerateRandomDeviceName@@YAJPEAU_UNICODE_STRING@@0@Z 00000001400391ee f   entry.obj
 0001:00038c8a       ?ReadVirtualMemory@@YAK_KPEAX1PEAK@Z 0000000140039c8a f   entry.obj
 0001:00039f90       ?ReadPhysicalMemory@@YAK_KPEAXKPEAK@Z 000000014003af90 f   entry.obj
 0001:0003a8f6       ?DmaDeviceControl@@YAJPEAU_DEVICE_OBJECT@@PEAU_IRP@@@Z 000000014003b8f6 f   entry.obj
 0001:0003b0da       ?DmaDispatchCreateClose@@YAJPEAU_DEVICE_OBJECT@@PEAU_IRP@@@Z 000000014003c0da f   entry.obj
 0001:0003b171       DriverEntry                000000014003c171 f   entry.obj
 0001:00072a90       __cpu_features_init        0000000140073a90 f   ntoskrnl:cpuinfo.obj
 0001:00072b50       _guard_check_icall_nop     0000000140073b50 f   ntoskrnl:guard_check.obj
 0001:00072b70       _guard_dispatch_icall_nop  0000000140073b70 f   ntoskrnl:guard_dispatch.obj
 0001:00072b90       _guard_xfg_dispatch_icall_nop 0000000140073b90 f   ntoskrnl:guard_xfg_dispatch.obj
 0001:00072bc0       memmove                    0000000140073bc0 f   ntoskrnl:memcpy.obj
 0001:00072bc0       memcpy                     0000000140073bc0 f   ntoskrnl:memcpy.obj
 0001:00072e80       memset                     0000000140073e80 f   ntoskrnl:memset.obj
 0001:00072e80       memset_$fo_default$        0000000140073e80 f   ntoskrnl:memset.obj
 0001:00072fc0       __memset_repmovs           0000000140073fc0 f   ntoskrnl:memset.obj
 0001:00073040       __memset_query             0000000140074040 f   ntoskrnl:memset.obj
 0001:00073080       __uma_functions            0000000140074080     BufferOverflowFastFailK:loadcfg.obj
 0001:000731d0       __guard_check_icall_fptr   00000001400741d0     ntoskrnl:guard_support.obj
 0001:000731d8       __guard_dispatch_icall_fptr 00000001400741d8     ntoskrnl:guard_support.obj
 0001:000731e0       __guard_xfg_check_icall_fptr 00000001400741e0     ntoskrnl:guard_support.obj
 0001:000731e8       __guard_xfg_dispatch_icall_fptr 00000001400741e8     ntoskrnl:guard_support.obj
 0001:000731f0       __guard_xfg_table_dispatch_icall_fptr 00000001400741f0     ntoskrnl:guard_support.obj
 0001:000731f8       __castguard_check_failure_os_handled_fptr 00000001400741f8     ntoskrnl:guard_support.obj
 0001:000733e0       _load_config_used          00000001400743e0     BufferOverflowFastFailK:loadcfg.obj
 0002:00007c40       ?base@cache@kli@@3_KA      000000014007cc40     Anti4heatExpert.obj
 0002:0000fb00       __isa_info                 0000000140084b00     ntoskrnl:cpuinfo.obj
 0002:0000fb40       __security_cookie          0000000140084b40     BufferOverflowFastFailK:gs_support.obj
 0002:0000fb50       ?g_PteBase@Anti4heatExpert@@3_KA 0000000140084b50     Anti4heatExpert.obj
 0002:0000fb58       ?g_PdeBase@Anti4heatExpert@@3_KA 0000000140084b58     Anti4heatExpert.obj
 0002:0000fb60       ?g_PpeBase@Anti4heatExpert@@3_KA 0000000140084b60     Anti4heatExpert.obj
 0002:0000fb68       ?g_PxeBase@Anti4heatExpert@@3_KA 0000000140084b68     Anti4heatExpert.obj
 0002:0000fb70       ?g_IsInitPteBaseForSystem@Anti4heatExpert@@3EA 0000000140084b70     Anti4heatExpert.obj
 0002:0000fb78       ?g_PhysicalMemoryRanges@Anti4heatExpert@@3PEAU_PHYSICAL_MEMORY_RANGE@@EA 0000000140084b78     Anti4heatExpert.obj
 0002:0000fb80       ?KLIKeGetCurrentIrql@@3P6AEXZEA 0000000140084b80     Anti4heatExpert.obj
 0002:0000fb88       ?KLIMmGetPhysicalMemoryRanges@@3P6APEAU_PHYSICAL_MEMORY_RANGE@@XZEA 0000000140084b88     Anti4heatExpert.obj
 0002:0000fb90       ?KLIMmGetPhysicalAddress@@3P6A?AT_LARGE_INTEGER@@PEAX@ZEA 0000000140084b90     Anti4heatExpert.obj
 0002:0000fb98       ?KLIMmGetVirtualForPhysical@@3P6APEAXT_LARGE_INTEGER@@@ZEA 0000000140084b98     Anti4heatExpert.obj
 0002:0000fba0       ?KLIMmAllocateMappingAddress@@3P6APEAX_KK@ZEA 0000000140084ba0     Anti4heatExpert.obj
 0002:0000fba8       ?KLIMmFreeMappingAddress@@3P6AXPEAXK@ZEA 0000000140084ba8     Anti4heatExpert.obj
 0002:0000fbb0       ?KLIIoCreateDevice@@3P6AJPEAU_DRIVER_OBJECT@@KPEAU_UNICODE_STRING@@KKEPEAPEAU_DEVICE_OBJECT@@@ZEA 0000000140084bb0     Anti4heatExpert.obj
 0002:0000fbb8       ?KLIIoCreateSymbolicLink@@3P6AJPEAU_UNICODE_STRING@@0@ZEA 0000000140084bb8     Anti4heatExpert.obj
 0002:0000fbc0       ?KLIIoDeleteDevice@@3P6AXPEAU_DEVICE_OBJECT@@@ZEA 0000000140084bc0     Anti4heatExpert.obj
 0002:0000fbc8       ?KLIIoDeleteSymbolicLink@@3P6AJPEAU_UNICODE_STRING@@@ZEA 0000000140084bc8     Anti4heatExpert.obj
 0002:0000fbd0       ?KLIKeQueryTimeIncrement@@3P6AKXZEA 0000000140084bd0     Anti4heatExpert.obj
 0002:0000fbd8       _fltused                   0000000140084bd8     entry.obj
 0002:0000fbe0       ?g_DeviceName@@3U_UNICODE_STRING@@A 0000000140084be0     entry.obj
 0002:0000fbf0       ?g_DosDeviceName@@3U_UNICODE_STRING@@A 0000000140084bf0     entry.obj
 0002:0000fd58       ?KLIExAllocatePool2@@3P6APEAX_K0K@ZEA 0000000140084d58     entry.obj
 0002:0000fd60       ?KLIExFreePool@@3P6AXPEAX@ZEA 0000000140084d60     entry.obj
 0002:0000fd68       ?KLIRtlStringCchPrintfW@@3P6AJPEA_W_KPEB_WZZEA 0000000140084d68     entry.obj
 0002:0000fd70       ?KLIRtlInitUnicodeString@@3P6AXPEAU_UNICODE_STRING@@PEB_W@ZEA 0000000140084d70     entry.obj
 0002:0000fd78       ?KLIIofCompleteRequest@@3P6AXPEAU_IRP@@D@ZEA 0000000140084d78     entry.obj
 0002:0000fd80       ?KLIDbgPrint@@3P6AKPEBDZZEA 0000000140084d80     entry.obj

 entry point at         0001:0003b171

 Static symbols

 0000:00000000       __guard_fids__             0000000140000000     ntoskrnl:guard_support.obj
 0000:00000000       __guard_fids__             0000000140000000     ntoskrnl:guard_support.obj
 0001:00071b98       ?__invoke@<lambda_0>@?0??DriverEntry@@9@CA@PEAU_DRIVER_OBJECT@@@Z 0000000140072b98 f   entry.obj
 0001:00071d60       .Lgoron_decrypt_string_0   0000000140072d60 f   entry.obj
 0001:00071e50       .Lgoron_decrypt_string_1   0000000140072e50 f   entry.obj
 0001:00071f40       .Lgoron_decrypt_string_2   0000000140072f40 f   entry.obj
 0001:00072020       .Lgoron_decrypt_string_3   0000000140073020 f   entry.obj
 0001:00072110       .Lgoron_decrypt_string_4   0000000140073110 f   entry.obj
 0001:00072200       .Lgoron_decrypt_string_5   0000000140073200 f   entry.obj
 0001:000722f0       .Lgoron_decrypt_string_6   00000001400732f0 f   entry.obj
 0001:000723f0       .Lgoron_decrypt_string_7   00000001400733f0 f   entry.obj
 0001:000724e0       .Lgoron_decrypt_string_8   00000001400734e0 f   entry.obj
 0001:000725d0       .Lgoron_decrypt_string_9   00000001400735d0 f   entry.obj
 0001:000726d0       g_DeviceFormat             00000001400736d0 f   entry.obj
 0001:00072720       g_DosFormat                0000000140073720 f   entry.obj
 0001:00072b70       $$000000                   0000000140073b70     ntoskrnl:guard_dispatch.obj
 0001:00072b90       $$000000                   0000000140073b90     ntoskrnl:guard_xfg_dispatch.obj
 0001:00072bc0       $$000000                   0000000140073bc0     ntoskrnl:memcpy.obj
 0001:00072be0       mcpy00                     0000000140073be0 f   ntoskrnl:memcpy.obj
 0001:00072c00       mcpy10                     0000000140073c00 f   ntoskrnl:memcpy.obj
 0001:00072c1a       mcpy20                     0000000140073c1a f   ntoskrnl:memcpy.obj
 0001:00072c20       mcpy30                     0000000140073c20 f   ntoskrnl:memcpy.obj
 0001:00072c40       xcpy00                     0000000140073c40 f   ntoskrnl:memcpy.obj
 0001:00072c71       xcpy10                     0000000140073c71 f   ntoskrnl:memcpy.obj
 0001:00072cc0       xcpy13                     0000000140073cc0 f   ntoskrnl:memcpy.obj
 0001:00072cef       xcpy15                     0000000140073cef f   ntoskrnl:memcpy.obj
 0001:00072d11       xcpy20                     0000000140073d11 f   ntoskrnl:memcpy.obj
 0001:00072d25       xcpy30                     0000000140073d25 f   ntoskrnl:memcpy.obj
 0001:00072d40       lcpy00                     0000000140073d40 f   ntoskrnl:memcpy.obj
 0001:00072d80       lcpy10                     0000000140073d80 f   ntoskrnl:memcpy.obj
 0001:00072dc0       xmov00                     0000000140073dc0 f   ntoskrnl:memcpy.obj
 0001:00072ded       xmov10                     0000000140073ded f   ntoskrnl:memcpy.obj
 0001:00072e00       xmov13                     0000000140073e00 f   ntoskrnl:memcpy.obj
 0001:00072e2f       xmov15                     0000000140073e2f f   ntoskrnl:memcpy.obj
 0001:00072e51       xmov20                     0000000140073e51 f   ntoskrnl:memcpy.obj
 0001:00072e66       xmov30                     0000000140073e66 f   ntoskrnl:memcpy.obj
 0001:00072e80       $$000000                   0000000140073e80     ntoskrnl:memset.obj
 0001:00072ea2       mset10                     0000000140073ea2 f   ntoskrnl:memset.obj
 0001:00072eb8       mset20                     0000000140073eb8 f   ntoskrnl:memset.obj
 0001:00072ee1       mset30                     0000000140073ee1 f   ntoskrnl:memset.obj
 0001:00072f10       mset45                     0000000140073f10 f   ntoskrnl:memset.obj
 0001:00072f16       mset46                     0000000140073f16 f   ntoskrnl:memset.obj
 0001:00072f40       mset47                     0000000140073f40 f   ntoskrnl:memset.obj
 0001:00072f70       mset48                     0000000140073f70 f   ntoskrnl:memset.obj
 0001:00072f86       mset50                     0000000140073f86 f   ntoskrnl:memset.obj
 0001:00072fc0       $$000000                   0000000140073fc0     ntoskrnl:memset.obj
 0001:00072fca       repmovs10                  0000000140073fca f   ntoskrnl:memset.obj
 0001:00072ffc       repmovs20                  0000000140073ffc f   ntoskrnl:memset.obj
 0001:00073040       $$000000                   0000000140074040     ntoskrnl:memset.obj
 0001:000733b0       $pdata$__cpu_features_init 00000001400743b0     ntoskrnl:cpuinfo.obj
 0001:00073728       $xdatasym$memcpy           0000000140074728     ntoskrnl:memcpy.obj
 0001:00073728       $xdatasym                  0000000140074728     ntoskrnl:memcpy.obj
 0001:00073728       $xdatasym$memset           0000000140074728     ntoskrnl:memset.obj
 0001:00073728       $xdatasym$_guard_dispatch_icall_nop 0000000140074728     ntoskrnl:guard_dispatch.obj
 0001:00073728       $xdatasym$_guard_xfg_dispatch_icall_nop 0000000140074728     ntoskrnl:guard_xfg_dispatch.obj
 0001:00073730       $xdatasym$__memset_repmovs 0000000140074730     ntoskrnl:memset.obj
 0001:00073730       $xdatasym                  0000000140074730     ntoskrnl:memset.obj
 0001:00073740       $xdatasym$__memset_query   0000000140074740     ntoskrnl:memset.obj
 0001:00073754       $unwind$__cpu_features_init 0000000140074754     ntoskrnl:cpuinfo.obj
 0001:00073760       $xdatasym                  0000000140074760     ntoskrnl:memset_spec_ermsb.obj
 0001:00073760       $xdatasym                  0000000140074760     ntoskrnl:memset_spec_plain.obj
 0001:00073760       $xdatasym                  0000000140074760     ntoskrnl:guard_dispatch.obj
 0001:00073760       $xdatasym                  0000000140074760     ntoskrnl:guard_xfg_dispatch.obj
