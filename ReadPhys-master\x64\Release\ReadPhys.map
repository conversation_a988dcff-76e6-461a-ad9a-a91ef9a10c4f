 ReadPhys

 Timestamp is 688e72a3 (Sun Aug  3 04:18:43 2025)

 Preferred load address is 0000000140000000

 Start         Length     Name                   Class
 0001:00000000 00072b3eH .text                   CODE
 0001:00072b40 000000d0H .text$mn                CODE
 0001:00072c10 00000036H .text$mn$00             CODE
 0001:00072c80 000004c0H .text$mn$21             CODE
 0001:00073140 00000180H .00cfg                  CODE
 0001:000732c0 000001d4H .pdata                  CODE
 0001:000734a0 00000148H .rdata                  CODE
 0001:00073618 00000240H .xdata                  CODE
 0002:00000000 0000fb48H .data                   DATA
 0002:0000fb48 00000238H .bss                    DATA

  Address         Publics by Value              Rva+Base               Lib:Object

 0000:00000000       __guard_iat_table          0000000000000000     <absolute>
 0000:00000000       __guard_iat_count          0000000000000000     <absolute>
 0000:00000000       __guard_longjmp_table      0000000000000000     <absolute>
 0000:00000000       __guard_longjmp_count      0000000000000000     <absolute>
 0000:00000000       __guard_eh_cont_table      0000000000000000     <absolute>
 0000:00000000       __guard_eh_cont_count      0000000000000000     <absolute>
 0000:0000000c       __guard_fids_count         000000000000000c     <absolute>
 0000:00010500       __guard_flags              0000000000010500     <absolute>
 0001:00000000       ?InitializeKliCache@Anti4heatExpert@@YAXXZ 0000000140001000 f   Anti4heatExpert.obj
 0001:00031d0f       ?RaiseIRQL@Anti4heatExpert@@YAEE@Z 0000000140032d0f f   Anti4heatExpert.obj
 0001:00031d30       ?RaiseIrqlToDpcLv@Anti4heatExpert@@YAEXZ 0000000140032d30 f   Anti4heatExpert.obj
 0001:00031d7e       ?LowerIrql@Anti4heatExpert@@YAXE@Z 0000000140032d7e f   Anti4heatExpert.obj
 0001:00031d86       ?IsPhysPageInRange@Anti4heatExpert@@YA_N_K0@Z 0000000140032d86 f   Anti4heatExpert.obj
 0001:00032346       ?IsVaPhysicalAddressValid@Anti4heatExpert@@YA_NPEAX@Z 0000000140033346 f   Anti4heatExpert.obj
 0001:00032382       ?GetPML4Base@Anti4heatExpert@@YAPEAXT_LARGE_INTEGER@@@Z 0000000140033382 f   Anti4heatExpert.obj
 0001:0003243f       ?InitializePteBase@Anti4heatExpert@@YAKXZ 000000014003343f f   Anti4heatExpert.obj
 0001:00032a2f       ?GetPteAddress@Anti4heatExpert@@YA_KPEAX@Z 0000000140033a2f f   Anti4heatExpert.obj
 0001:00032a90       ?AllocatePhysicalPage@Anti4heatExpert@@YAKPEAUPHYSICAL_PAGE_INFO@1@_K@Z 0000000140033a90 f   Anti4heatExpert.obj
 0001:000331bb       ?FreePhysicalPage@Anti4heatExpert@@YAXPEAUPHYSICAL_PAGE_INFO@1@@Z 00000001400341bb f   Anti4heatExpert.obj
 0001:000332db       ?ReadPhysicalPage@Anti4heatExpert@@YAKPEAUPHYSICAL_PAGE_INFO@1@_KPEAX1@Z 00000001400342db f   Anti4heatExpert.obj
 0001:00033c3e       ?GetPageTableInfo@Anti4heatExpert@@YAKPEAUPHYSICAL_PAGE_INFO@1@_K1PEAUPAGE_TABLE_INFO@1@@Z 0000000140034c3e f   Anti4heatExpert.obj
 0001:00034792       ?GetPhysPageAddress@Anti4heatExpert@@YAKPEAUPHYSICAL_PAGE_INFO@1@_KPEAXPEA_K@Z 0000000140035792 f   Anti4heatExpert.obj
 0001:00034f2b       ?GetPhysPageSize@Anti4heatExpert@@YAKPEAUPHYSICAL_PAGE_INFO@1@_KPEA_K1@Z 0000000140035f2b f   Anti4heatExpert.obj
 0001:00037d5d       ?get_kernel_base@detail@kli@@YA_KXZ 0000000140038d5d f   Anti4heatExpert.obj
 0001:00037ddc       GUID_ECP_FLT_CREATEFILE_TARGET 0000000140038ddc f   Anti4heatExpert.obj
 0001:00037df0       __chkstk                   0000000140038df0 f   entry.obj
 0001:00037df1       ?DeobfuscateString@@YAXPEA_WPEBEKE@Z 0000000140038df1 f   entry.obj
 0001:00037eef       ?SimpleHash@@YAKPEBDK@Z    0000000140038eef f   entry.obj
 0001:00037fb6       ?GeneratePredictableGUID@@YAXPEAK000@Z 0000000140038fb6 f   entry.obj
 0001:00038388       ?GenerateRandomDeviceName@@YAJPEAU_UNICODE_STRING@@0@Z 0000000140039388 f   entry.obj
 0001:00038eef       ?ReadVirtualMemory@@YAK_KPEAX1PEAK@Z 0000000140039eef f   entry.obj
 0001:0003a148       ?ReadPhysicalMemory@@YAK_KPEAXKPEAK@Z 000000014003b148 f   entry.obj
 0001:0003aaa0       ?DmaDeviceControl@@YAJPEAU_DEVICE_OBJECT@@PEAU_IRP@@@Z 000000014003baa0 f   entry.obj
 0001:0003b2bd       ?DmaDispatchCreateClose@@YAJPEAU_DEVICE_OBJECT@@PEAU_IRP@@@Z 000000014003c2bd f   entry.obj
 0001:0003b349       DriverEntry                000000014003c349 f   entry.obj
 0001:00072b40       _guard_check_icall_nop     0000000140073b40 f   ntoskrnl:guard_check.obj
 0001:00072b50       __cpu_features_init        0000000140073b50 f   ntoskrnl:cpuinfo.obj
 0001:00072c20       _guard_dispatch_icall_nop  0000000140073c20 f   ntoskrnl:guard_dispatch.obj
 0001:00072c40       _guard_xfg_dispatch_icall_nop 0000000140073c40 f   ntoskrnl:guard_xfg_dispatch.obj
 0001:00072c80       memmove                    0000000140073c80 f   ntoskrnl:memcpy.obj
 0001:00072c80       memcpy                     0000000140073c80 f   ntoskrnl:memcpy.obj
 0001:00072f40       memset                     0000000140073f40 f   ntoskrnl:memset.obj
 0001:00072f40       memset_$fo_default$        0000000140073f40 f   ntoskrnl:memset.obj
 0001:00073080       __memset_repmovs           0000000140074080 f   ntoskrnl:memset.obj
 0001:00073100       __memset_query             0000000140074100 f   ntoskrnl:memset.obj
 0001:00073140       __guard_check_icall_fptr   0000000140074140     ntoskrnl:guard_support.obj
 0001:00073148       __guard_dispatch_icall_fptr 0000000140074148     ntoskrnl:guard_support.obj
 0001:00073150       __guard_xfg_check_icall_fptr 0000000140074150     ntoskrnl:guard_support.obj
 0001:00073158       __guard_xfg_dispatch_icall_fptr 0000000140074158     ntoskrnl:guard_support.obj
 0001:00073160       __guard_xfg_table_dispatch_icall_fptr 0000000140074160     ntoskrnl:guard_support.obj
 0001:00073168       __castguard_check_failure_os_handled_fptr 0000000140074168     ntoskrnl:guard_support.obj
 0001:00073170       __uma_functions            0000000140074170     BufferOverflowFastFailK:loadcfg.obj
 0001:000734a0       _load_config_used          00000001400744a0     BufferOverflowFastFailK:loadcfg.obj
 0000:00000000       __guard_fids_table         00000001400745e8     <linker-defined>
 0002:00007c40       ?base@cache@kli@@3_KA      000000014007cc40     Anti4heatExpert.obj
 0002:0000fb00       __isa_info                 0000000140084b00     ntoskrnl:cpuinfo.obj
 0002:0000fb40       __security_cookie          0000000140084b40     BufferOverflowFastFailK:gs_support.obj
 0002:0000fb48       ?g_PteBase@Anti4heatExpert@@3_KA 0000000140084b48     Anti4heatExpert.obj
 0002:0000fb50       ?g_PdeBase@Anti4heatExpert@@3_KA 0000000140084b50     Anti4heatExpert.obj
 0002:0000fb58       ?g_PpeBase@Anti4heatExpert@@3_KA 0000000140084b58     Anti4heatExpert.obj
 0002:0000fb60       ?g_PxeBase@Anti4heatExpert@@3_KA 0000000140084b60     Anti4heatExpert.obj
 0002:0000fb68       ?g_IsInitPteBaseForSystem@Anti4heatExpert@@3EA 0000000140084b68     Anti4heatExpert.obj
 0002:0000fb70       ?g_PhysicalMemoryRanges@Anti4heatExpert@@3PEAU_PHYSICAL_MEMORY_RANGE@@EA 0000000140084b70     Anti4heatExpert.obj
 0002:0000fb78       ?KLIKeGetCurrentIrql@@3P6AEXZEA 0000000140084b78     Anti4heatExpert.obj
 0002:0000fb80       ?KLIMmGetPhysicalMemoryRanges@@3P6APEAU_PHYSICAL_MEMORY_RANGE@@XZEA 0000000140084b80     Anti4heatExpert.obj
 0002:0000fb88       ?KLIMmGetPhysicalAddress@@3P6A?AT_LARGE_INTEGER@@PEAX@ZEA 0000000140084b88     Anti4heatExpert.obj
 0002:0000fb90       ?KLIMmGetVirtualForPhysical@@3P6APEAXT_LARGE_INTEGER@@@ZEA 0000000140084b90     Anti4heatExpert.obj
 0002:0000fb98       ?KLIMmAllocateMappingAddress@@3P6APEAX_KK@ZEA 0000000140084b98     Anti4heatExpert.obj
 0002:0000fba0       ?KLIMmFreeMappingAddress@@3P6AXPEAXK@ZEA 0000000140084ba0     Anti4heatExpert.obj
 0002:0000fba8       ?KLIIoCreateDevice@@3P6AJPEAU_DRIVER_OBJECT@@KPEAU_UNICODE_STRING@@KKEPEAPEAU_DEVICE_OBJECT@@@ZEA 0000000140084ba8     Anti4heatExpert.obj
 0002:0000fbb0       ?KLIIoCreateSymbolicLink@@3P6AJPEAU_UNICODE_STRING@@0@ZEA 0000000140084bb0     Anti4heatExpert.obj
 0002:0000fbb8       ?KLIIoDeleteDevice@@3P6AXPEAU_DEVICE_OBJECT@@@ZEA 0000000140084bb8     Anti4heatExpert.obj
 0002:0000fbc0       ?KLIIoDeleteSymbolicLink@@3P6AJPEAU_UNICODE_STRING@@@ZEA 0000000140084bc0     Anti4heatExpert.obj
 0002:0000fbc8       ?KLIKeQueryTimeIncrement@@3P6AKXZEA 0000000140084bc8     Anti4heatExpert.obj
 0002:0000fbd0       _fltused                   0000000140084bd0     entry.obj
 0002:0000fbd8       ?g_DeviceName@@3U_UNICODE_STRING@@A 0000000140084bd8     entry.obj
 0002:0000fbe8       ?g_DosDeviceName@@3U_UNICODE_STRING@@A 0000000140084be8     entry.obj
 0002:0000fd50       ?KLIExAllocatePool2@@3P6APEAX_K0K@ZEA 0000000140084d50     entry.obj
 0002:0000fd58       ?KLIExFreePool@@3P6AXPEAX@ZEA 0000000140084d58     entry.obj
 0002:0000fd60       ?KLIRtlStringCchPrintfW@@3P6AJPEA_W_KPEB_WZZEA 0000000140084d60     entry.obj
 0002:0000fd68       ?KLIRtlInitUnicodeString@@3P6AXPEAU_UNICODE_STRING@@PEB_W@ZEA 0000000140084d68     entry.obj
 0002:0000fd70       ?KLIIofCompleteRequest@@3P6AXPEAU_IRP@@D@ZEA 0000000140084d70     entry.obj
 0002:0000fd78       ?KLIDbgPrint@@3P6AKPEBDZZEA 0000000140084d78     entry.obj

 entry point at         0001:0003b349

 Static symbols

 0000:00000000       __guard_fids__             0000000140000000     ntoskrnl:guard_support.obj
 0000:00000000       __guard_fids__             0000000140000000     ntoskrnl:guard_support.obj
 0001:00071c50       ?__invoke@<lambda_0>@?0??DriverEntry@@9@CA@PEAU_DRIVER_OBJECT@@@Z 0000000140072c50 f   entry.obj
 0001:00071e30       .Lgoron_decrypt_string_0   0000000140072e30 f   entry.obj
 0001:00071f20       .Lgoron_decrypt_string_1   0000000140072f20 f   entry.obj
 0001:00072020       .Lgoron_decrypt_string_2   0000000140073020 f   entry.obj
 0001:00072120       .Lgoron_decrypt_string_3   0000000140073120 f   entry.obj
 0001:00072210       .Lgoron_decrypt_string_4   0000000140073210 f   entry.obj
 0001:00072300       .Lgoron_decrypt_string_5   0000000140073300 f   entry.obj
 0001:000723e0       .Lgoron_decrypt_string_6   00000001400733e0 f   entry.obj
 0001:000724d0       .Lgoron_decrypt_string_7   00000001400734d0 f   entry.obj
 0001:000725b0       .Lgoron_decrypt_string_8   00000001400735b0 f   entry.obj
 0001:000726a0       .Lgoron_decrypt_string_9   00000001400736a0 f   entry.obj
 0001:00072790       g_DeviceFormat             0000000140073790 f   entry.obj
 0001:000727e0       g_DosFormat                00000001400737e0 f   entry.obj
 0001:00072c20       $$000000                   0000000140073c20     ntoskrnl:guard_dispatch.obj
 0001:00072c40       $$000000                   0000000140073c40     ntoskrnl:guard_xfg_dispatch.obj
 0001:00072c80       $$000000                   0000000140073c80     ntoskrnl:memcpy.obj
 0001:00072ca0       mcpy00                     0000000140073ca0 f   ntoskrnl:memcpy.obj
 0001:00072cc0       mcpy10                     0000000140073cc0 f   ntoskrnl:memcpy.obj
 0001:00072cda       mcpy20                     0000000140073cda f   ntoskrnl:memcpy.obj
 0001:00072ce0       mcpy30                     0000000140073ce0 f   ntoskrnl:memcpy.obj
 0001:00072d00       xcpy00                     0000000140073d00 f   ntoskrnl:memcpy.obj
 0001:00072d31       xcpy10                     0000000140073d31 f   ntoskrnl:memcpy.obj
 0001:00072d80       xcpy13                     0000000140073d80 f   ntoskrnl:memcpy.obj
 0001:00072daf       xcpy15                     0000000140073daf f   ntoskrnl:memcpy.obj
 0001:00072dd1       xcpy20                     0000000140073dd1 f   ntoskrnl:memcpy.obj
 0001:00072de5       xcpy30                     0000000140073de5 f   ntoskrnl:memcpy.obj
 0001:00072e00       lcpy00                     0000000140073e00 f   ntoskrnl:memcpy.obj
 0001:00072e40       lcpy10                     0000000140073e40 f   ntoskrnl:memcpy.obj
 0001:00072e80       xmov00                     0000000140073e80 f   ntoskrnl:memcpy.obj
 0001:00072ead       xmov10                     0000000140073ead f   ntoskrnl:memcpy.obj
 0001:00072ec0       xmov13                     0000000140073ec0 f   ntoskrnl:memcpy.obj
 0001:00072eef       xmov15                     0000000140073eef f   ntoskrnl:memcpy.obj
 0001:00072f11       xmov20                     0000000140073f11 f   ntoskrnl:memcpy.obj
 0001:00072f26       xmov30                     0000000140073f26 f   ntoskrnl:memcpy.obj
 0001:00072f40       $$000000                   0000000140073f40     ntoskrnl:memset.obj
 0001:00072f62       mset10                     0000000140073f62 f   ntoskrnl:memset.obj
 0001:00072f78       mset20                     0000000140073f78 f   ntoskrnl:memset.obj
 0001:00072fa1       mset30                     0000000140073fa1 f   ntoskrnl:memset.obj
 0001:00072fd0       mset45                     0000000140073fd0 f   ntoskrnl:memset.obj
 0001:00072fd6       mset46                     0000000140073fd6 f   ntoskrnl:memset.obj
 0001:00073000       mset47                     0000000140074000 f   ntoskrnl:memset.obj
 0001:00073030       mset48                     0000000140074030 f   ntoskrnl:memset.obj
 0001:00073046       mset50                     0000000140074046 f   ntoskrnl:memset.obj
 0001:00073080       $$000000                   0000000140074080     ntoskrnl:memset.obj
 0001:0007308a       repmovs10                  000000014007408a f   ntoskrnl:memset.obj
 0001:000730bc       repmovs20                  00000001400740bc f   ntoskrnl:memset.obj
 0001:00073100       $$000000                   0000000140074100     ntoskrnl:memset.obj
 0001:00073488       $pdata$__cpu_features_init 0000000140074488     ntoskrnl:cpuinfo.obj
 0001:00073818       $xdatasym$memcpy           0000000140074818     ntoskrnl:memcpy.obj
 0001:00073818       $xdatasym                  0000000140074818     ntoskrnl:memcpy.obj
 0001:00073818       $xdatasym$memset           0000000140074818     ntoskrnl:memset.obj
 0001:00073818       $xdatasym$_guard_dispatch_icall_nop 0000000140074818     ntoskrnl:guard_dispatch.obj
 0001:00073818       $xdatasym$_guard_xfg_dispatch_icall_nop 0000000140074818     ntoskrnl:guard_xfg_dispatch.obj
 0001:00073820       $xdatasym$__memset_repmovs 0000000140074820     ntoskrnl:memset.obj
 0001:00073820       $xdatasym                  0000000140074820     ntoskrnl:memset.obj
 0001:00073830       $xdatasym$__memset_query   0000000140074830     ntoskrnl:memset.obj
 0001:00073848       $xdatasym                  0000000140074848     ntoskrnl:guard_dispatch.obj
 0001:00073848       $xdatasym                  0000000140074848     ntoskrnl:guard_xfg_dispatch.obj
 0001:00073848       $unwind$__cpu_features_init 0000000140074848     ntoskrnl:cpuinfo.obj
 0001:00073858       $xdatasym                  0000000140074858     ntoskrnl:memset_spec_ermsb.obj
 0001:00073858       $xdatasym                  0000000140074858     ntoskrnl:memset_spec_plain.obj
