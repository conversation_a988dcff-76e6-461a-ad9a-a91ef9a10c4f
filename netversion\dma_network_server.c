#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <stdio.h>
#include "dma_network_protocol.h"

#pragma comment(lib, "ws2_32.lib")

// �ں������ӿڶ��壨����Ҫ����ʵ������������
#define DRIVER_NAME L"\\\\.\\YourDmaDriver"
#define IOCTL_READ_PHYSICAL_MEMORY  CTL_CODE(FILE_DEVICE_UNKNOWN, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_WRITE_PHYSICAL_MEMORY CTL_CODE(FILE_DEVICE_UNKNOWN, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)

typedef struct _PHYSICAL_MEMORY_REQUEST {
    QWORD PhysicalAddress;
    DWORD Length;
    UCHAR Data[1];  // �ɱ䳤������
} PHYSICAL_MEMORY_REQUEST, *PPHYSICAL_MEMORY_REQUEST;

// ȫ�ַ�����״̬
static SOCKET g_serverSocket = INVALID_SOCKET;
static HANDLE g_driverHandle = INVALID_HANDLE_VALUE;
static BOOL g_running = FALSE;

// ���ں�����
static BOOL OpenKernelDriver() {
    g_driverHandle = CreateFileW(
        DRIVER_NAME,
        GENERIC_READ | GENERIC_WRITE,
        0,
        NULL,
        OPEN_EXISTING,
        FILE_ATTRIBUTE_NORMAL,
        NULL
    );
    
    return (g_driverHandle != INVALID_HANDLE_VALUE);
}

// ͨ��������ȡ�����ڴ�
static BOOL ReadPhysicalMemory(QWORD address, PVOID buffer, DWORD length, PDWORD bytesRead) {
    if (g_driverHandle == INVALID_HANDLE_VALUE) {
        return FALSE;
    }
    
    // �������󻺳���
    DWORD requestSize = sizeof(PHYSICAL_MEMORY_REQUEST) + length;
    PPHYSICAL_MEMORY_REQUEST request = (PPHYSICAL_MEMORY_REQUEST)malloc(requestSize);
    if (!request) {
        return FALSE;
    }
    
    request->PhysicalAddress = address;
    request->Length = length;
    
    DWORD returned;
    BOOL result = DeviceIoControl(
        g_driverHandle,
        IOCTL_READ_PHYSICAL_MEMORY,
        request,
        sizeof(PHYSICAL_MEMORY_REQUEST),
        request,
        requestSize,
        &returned,
        NULL
    );
    
    if (result && returned >= sizeof(PHYSICAL_MEMORY_REQUEST)) {
        DWORD dataSize = returned - sizeof(PHYSICAL_MEMORY_REQUEST) + 1;
        if (dataSize <= length) {
            memcpy(buffer, request->Data, dataSize);
            if (bytesRead) {
                *bytesRead = dataSize;
            }
        } else {
            result = FALSE;
        }
    }
    
    free(request);
    return result;
}

// ͨ������д�������ڴ�
static BOOL WritePhysicalMemory(QWORD address, PVOID buffer, DWORD length, PDWORD bytesWritten) {
    if (g_driverHandle == INVALID_HANDLE_VALUE) {
        return FALSE;
    }
    
    // �������󻺳���
    DWORD requestSize = sizeof(PHYSICAL_MEMORY_REQUEST) + length;
    PPHYSICAL_MEMORY_REQUEST request = (PPHYSICAL_MEMORY_REQUEST)malloc(requestSize);
    if (!request) {
        return FALSE;
    }
    
    request->PhysicalAddress = address;
    request->Length = length;
    memcpy(request->Data, buffer, length);
    
    DWORD returned;
    BOOL result = DeviceIoControl(
        g_driverHandle,
        IOCTL_WRITE_PHYSICAL_MEMORY,
        request,
        requestSize,
        NULL,
        0,
        &returned,
        NULL
    );
    
    if (result && bytesWritten) {
        *bytesWritten = length;
    }
    
    free(request);
    return result;
}

// ������Ӧ
static BOOL SendResponse(SOCKET clientSocket, DWORD status, PVOID data, DWORD dataLength) {
    DMA_RESPONSE response;
    response.magic = DMA_MAGIC;
    response.status = status;
    response.length = dataLength;
    response.reserved = 0;
    
    // ������Ӧͷ
    if (send(clientSocket, (char*)&response, sizeof(response), 0) == SOCKET_ERROR) {
        return FALSE;
    }
    
    // �������ݣ�����У�
    if (dataLength > 0 && data) {
        DWORD totalSent = 0;
        while (totalSent < dataLength) {
            int sent = send(clientSocket, (char*)data + totalSent, dataLength - totalSent, 0);
            if (sent == SOCKET_ERROR) {
                return FALSE;
            }
            totalSent += sent;
        }
    }
    
    return TRUE;
}

// �����ͻ�������
static DWORD WINAPI HandleClient(LPVOID param) {
    SOCKET clientSocket = (SOCKET)param;
    DMA_PACKET_HEADER header;
    PVOID buffer = NULL;
    
    printf("�ͻ�������: %p\\n", (void*)clientSocket);
    
    while (g_running) {
        // ��������ͷ
        int received = recv(clientSocket, (char*)&header, sizeof(header), 0);
        if (received != sizeof(header)) {
            break;
        }
        
        // ��֤Э��
        if (header.magic != DMA_MAGIC) {
            printf("��Ч��Э���ʶ\\n");
            break;
        }
        
        // ��鳤������
        if (header.length > DMA_MAX_TRANSFER_SIZE) {
            SendResponse(clientSocket, 1, NULL, 0);  // ����״̬
            continue;
        }
        
        // ���仺����
        if (header.length > 0) {
            buffer = malloc(header.length);
            if (!buffer) {
                SendResponse(clientSocket, 1, NULL, 0);
                continue;
            }
        }
        
        switch (header.command) {
            case DMA_CMD_READ: {
                printf("��ȡ����: ��ַ=0x%llx, ����=%d\\n", header.address, header.length);
                
                DWORD bytesRead = 0;
                BOOL success = ReadPhysicalMemory(header.address, buffer, header.length, &bytesRead);
                
                if (success) {
                    SendResponse(clientSocket, 0, buffer, bytesRead);
                } else {
                    SendResponse(clientSocket, 1, NULL, 0);
                }
                break;
            }
            
            case DMA_CMD_WRITE: {
                printf("д������: ��ַ=0x%llx, ����=%d\\n", header.address, header.length);
                
                // ����Ҫд�������
                if (header.length > 0) {
                    DWORD totalReceived = 0;
                    while (totalReceived < header.length) {
                        int recv_result = recv(clientSocket, (char*)buffer + totalReceived, 
                                             header.length - totalReceived, 0);
                        if (recv_result <= 0) {
                            break;
                        }
                        totalReceived += recv_result;
                    }
                    
                    if (totalReceived != header.length) {
                        SendResponse(clientSocket, 1, NULL, 0);
                        break;
                    }
                }
                
                DWORD bytesWritten = 0;
                BOOL success = WritePhysicalMemory(header.address, buffer, header.length, &bytesWritten);
                
                SendResponse(clientSocket, success ? 0 : 1, NULL, 0);
                break;
            }
            
            default:
                printf("δ֪����: %d\\n", header.command);
                SendResponse(clientSocket, 1, NULL, 0);
                break;
        }
        
        if (buffer) {
            free(buffer);
            buffer = NULL;
        }
    }
    
    printf("�ͻ��˶Ͽ�: %p\\n", (void*)clientSocket);
    closesocket(clientSocket);
    return 0;
}

// ��ʼ��������
BOOL DmaServer_Initialize(WORD port) {
    WSADATA wsaData;
    
    // ��ʼ��Winsock
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        printf("WSAStartupʧ��\\n");
        return FALSE;
    }
    
    // ���ں�����
    if (!OpenKernelDriver()) {
        printf("���ں�����ʧ��\\n");
        WSACleanup();
        return FALSE;
    }
    
    // ����������socket
    g_serverSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (g_serverSocket == INVALID_SOCKET) {
        printf("����socketʧ��\\n");
        CloseHandle(g_driverHandle);
        WSACleanup();
        return FALSE;
    }
    
    // ���õ�ַ����
    int opt = 1;
    setsockopt(g_serverSocket, SOL_SOCKET, SO_REUSEADDR, (char*)&opt, sizeof(opt));
    
    // �󶨵�ַ
    struct sockaddr_in serverAddr;
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_addr.s_addr = INADDR_ANY;
    serverAddr.sin_port = htons(port);
    
    if (bind(g_serverSocket, (struct sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
        printf("�󶨵�ַʧ��\\n");
        closesocket(g_serverSocket);
        CloseHandle(g_driverHandle);
        WSACleanup();
        return FALSE;
    }
    
    printf("DMA��������ʼ���ɹ��������˿�: %d\\n", port);
    return TRUE;
}

// ����������
BOOL DmaServer_Start() {
    if (listen(g_serverSocket, SOMAXCONN) == SOCKET_ERROR) {
        printf("����ʧ��\\n");
        return FALSE;
    }
    
    g_running = TRUE;
    printf("DMA�������������ȴ��ͻ�������...\\n");
    
    while (g_running) {
        SOCKET clientSocket = accept(g_serverSocket, NULL, NULL);
        if (clientSocket == INVALID_SOCKET) {
            if (g_running) {
                printf("��������ʧ��\\n");
            }
            continue;
        }
        
        // Ϊÿ���ͻ��˴��������߳�
        HANDLE hThread = CreateThread(NULL, 0, HandleClient, (LPVOID)clientSocket, 0, NULL);
        if (hThread) {
            CloseHandle(hThread);
        } else {
            closesocket(clientSocket);
        }
    }
    
    return TRUE;
}

// ֹͣ������
VOID DmaServer_Stop() {
    g_running = FALSE;
    
    if (g_serverSocket != INVALID_SOCKET) {
        closesocket(g_serverSocket);
        g_serverSocket = INVALID_SOCKET;
    }
    
    if (g_driverHandle != INVALID_HANDLE_VALUE) {
        CloseHandle(g_driverHandle);
        g_driverHandle = INVALID_HANDLE_VALUE;
    }
    
    WSACleanup();
    printf("DMA��������ֹͣ\\n");
}