# Makefile for DMA Network Solution
# 用于编译DMA网络替代方案

CC=gcc
CXX=g++
CFLAGS=-Wall -O2 -I.
CXXFLAGS=-Wall -O2 -I.
LDFLAGS=-lws2_32

# 目标文件
SERVER_TARGET=dma_server.exe
DLL_TARGET=winusb.dll

# 源文件
SERVER_SOURCES=dma_server_main.c dma_network_server.c
DLL_SOURCES=winusb_hook.cpp dma_network_client.c

.PHONY: all clean server dll

all: server dll

# 编译服务端程序
server: $(SERVER_TARGET)

$(SERVER_TARGET): $(SERVER_SOURCES)
	$(CC) $(CFLAGS) -o $@ $^ $(LDFLAGS)
	@echo "服务端程序编译完成: $(SERVER_TARGET)"

# 编译WinUSB劫持DLL
dll: $(DLL_TARGET)

$(DLL_TARGET): $(DLL_SOURCES)
	$(CXX) $(CXXFLAGS) -shared -o $@ $^ $(LDFLAGS) -Wl,--out-implib,winusb.lib
	@echo "劫持DLL编译完成: $(DLL_TARGET)"

# 清理编译文件
clean:
	rm -f $(SERVER_TARGET) $(DLL_TARGET) winusb.lib *.o
	@echo "清理完成"

# 安装部署
install: all
	@echo "请手动执行以下部署步骤:"
	@echo "1. 将 $(SERVER_TARGET) 复制到服务器机器"
	@echo "2. 将系统目录下的 winusb.dll 重命名为 winusb_orig.dll"
	@echo "3. 将编译的 $(DLL_TARGET) 复制到 LeechCore.dll 同目录"
	@echo "4. 修改 winusb_hook.cpp 中的服务器IP地址后重新编译"

# 帮助信息
help:
	@echo "可用目标:"
	@echo "  all     - 编译所有组件"
	@echo "  server  - 只编译服务端程序"
	@echo "  dll     - 只编译劫持DLL"
	@echo "  clean   - 清理编译文件"
	@echo "  install - 显示部署说明"
	@echo "  help    - 显示此帮助信息"