#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <stdio.h>
#include "../dma_network_protocol.h"

#pragma comment(lib, "ws2_32.lib")

#define DMA_SERVER_PORT 8888
#define MAX_CLIENTS 10

// LeechCore FPGA protocol analysis structures
typedef struct _TLP_HDR {
    WORD Length : 10;
    WORD _AT : 2;
    WORD _Attr : 2;
    WORD _EP : 1;
    WORD _TD : 1;
    BYTE _R1 : 4;
    BYTE _TC : 3;
    BYTE _R2 : 1;
    BYTE TypeFmt;
} TLP_HDR, *PTLP_HDR;

typedef struct _TLP_HDR_MRdWr32 {
    TLP_HDR h;
    BYTE FirstBE : 4;
    BYTE LastBE : 4;
    BYTE Tag;
    WORD RequesterID;
    DWORD Address;
} TLP_HDR_MRdWr32, *PTLP_HDR_MRdWr32;

typedef struct _TLP_HDR_MRdWr64 {
    TLP_HDR h;
    BYTE FirstBE : 4;
    BYTE LastBE : 4;
    BYTE Tag;
    WORD RequesterID;
    DWORD AddressHigh;
    DWORD AddressLow;
} TLP_HDR_MRdWr64, *PTLP_HDR_MRdWr64;

// TLP Type definitions
#define TLP_MRd32       0x00
#define TLP_MRd64       0x20
#define TLP_MWr32       0x40
#define TLP_MWr64       0x60

// FPGA protocol constants from LeechCore
#define FPGA_TLP_MAGIC          0x77000000
#define FPGA_TLP_VALID_LAST     0x77040000
#define FPGA_LOOPBACK_TX        0x77020000
#define FPGA_LOOPBACK_DATA      0xffeeddcc

// Kernel driver communication
static HANDLE g_driverHandle = INVALID_HANDLE_VALUE;

BOOL InitializeDriver() 
{
    if (g_driverHandle != INVALID_HANDLE_VALUE) {
        return TRUE;
    }
    
    // Open the kernel driver device
    g_driverHandle = CreateFileA(
        "\\\\.\\DmaDevice",  // Adjust device name as needed
        GENERIC_READ | GENERIC_WRITE,
        FILE_SHARE_READ | FILE_SHARE_WRITE,
        NULL,
        OPEN_EXISTING,
        FILE_ATTRIBUTE_NORMAL,
        NULL
    );
    
    return (g_driverHandle != INVALID_HANDLE_VALUE);
}

BOOL ProcessMemoryRead(QWORD address, PVOID buffer, DWORD length, PDWORD bytesRead)
{
    if (!InitializeDriver()) {
        return FALSE;
    }
    
    DMA_REQUEST request;
    request.Command = DMA_CMD_READ;
    request.Address = address;
    request.Length = length;
    
    DWORD bytesReturned;
    BOOL result = DeviceIoControl(
        g_driverHandle,
        IOCTL_DMA_READ_MEMORY,
        &request,
        sizeof(request),
        buffer,
        length,
        &bytesReturned,
        NULL
    );
    
    if (result && bytesRead) {
        *bytesRead = bytesReturned;
    }
    
    return result;
}

BOOL ProcessMemoryWrite(QWORD address, PVOID buffer, DWORD length, PDWORD bytesWritten)
{
    if (!InitializeDriver()) {
        return FALSE;
    }
    
    // Prepare buffer with request header and data
    DWORD totalSize = sizeof(DMA_REQUEST) + length;
    PUCHAR requestBuffer = (PUCHAR)malloc(totalSize);
    if (!requestBuffer) {
        return FALSE;
    }
    
    PDMA_REQUEST request = (PDMA_REQUEST)requestBuffer;
    request->Command = DMA_CMD_WRITE;
    request->Address = address;
    request->Length = length;
    
    // Copy data after the request header
    memcpy(requestBuffer + sizeof(DMA_REQUEST), buffer, length);
    
    DWORD bytesReturned;
    BOOL result = DeviceIoControl(
        g_driverHandle,
        IOCTL_DMA_WRITE_MEMORY,
        requestBuffer,
        totalSize,
        NULL,
        0,
        &bytesReturned,
        NULL
    );
    
    if (result && bytesWritten) {
        *bytesWritten = length;
    }
    
    free(requestBuffer);
    return result;
}

// Parse LeechCore FPGA protocol and extract TLP packets
BOOL ParseFPGAProtocol(PUCHAR buffer, DWORD bufferLength)
{
    DWORD i = 0;
    BYTE tlpBuffer[1024];
    DWORD tlpLength = 0;
    BOOL tlpInProgress = FALSE;
    
    printf("Parsing FPGA protocol buffer, length: %d\n", bufferLength);
    
    while (i < bufferLength) {
        DWORD dwData = *(PDWORD)(buffer + i);
        DWORD dwControl = *(PDWORD)(buffer + i + 4);
        
        printf("Offset %04x: Data=%08x Control=%08x\n", i, dwData, dwControl);
        
        // Check if this is a TLP data + control pair
        if (dwControl == FPGA_TLP_MAGIC || dwControl == FPGA_TLP_VALID_LAST) {
            // Add TLP data
            if (tlpLength + 4 <= sizeof(tlpBuffer)) {
                *(PDWORD)(tlpBuffer + tlpLength) = dwData;
                tlpLength += 4;
                tlpInProgress = TRUE;
            }
            
            // If this is the last TLP packet, process it
            if (dwControl == FPGA_TLP_VALID_LAST && tlpInProgress) {
                printf("Complete TLP found, length: %d\n", tlpLength);
                
                // Process the TLP packet
                if (tlpLength >= 12) {  // Minimum TLP header size
                    // Byte swap for analysis (LeechCore uses big-endian)
                    for (DWORD j = 0; j < tlpLength; j += 4) {
                        PDWORD pdw = (PDWORD)(tlpBuffer + j);
                        *pdw = _byteswap_ulong(*pdw);
                    }
                    
                    PTLP_HDR hdr = (PTLP_HDR)tlpBuffer;
                    printf("TLP Type: 0x%02x, Length: %d DWORDs\n", hdr->TypeFmt, hdr->Length);
                    
                    if (hdr->TypeFmt == TLP_MRd32) {
                        PTLP_HDR_MRdWr32 mrd = (PTLP_HDR_MRdWr32)tlpBuffer;
                        QWORD address = mrd->Address;
                        DWORD length = hdr->Length ? (hdr->Length << 2) : 0x1000;
                        
                        printf("Memory Read 32: Address=0x%08x, Length=%d\n", mrd->Address, length);
                        
                        // TODO: Perform actual memory read and send response
                        
                    } else if (hdr->TypeFmt == TLP_MRd64) {
                        PTLP_HDR_MRdWr64 mrd = (PTLP_HDR_MRdWr64)tlpBuffer;
                        QWORD address = ((QWORD)mrd->AddressHigh << 32) | mrd->AddressLow;
                        DWORD length = hdr->Length ? (hdr->Length << 2) : 0x1000;
                        
                        printf("Memory Read 64: Address=0x%016llx, Length=%d\n", address, length);
                        
                        // TODO: Perform actual memory read and send response
                        
                    } else if (hdr->TypeFmt == TLP_MWr32) {
                        PTLP_HDR_MRdWr32 mwr = (PTLP_HDR_MRdWr32)tlpBuffer;
                        QWORD address = mwr->Address;
                        DWORD length = hdr->Length ? (hdr->Length << 2) : 0x1000;
                        PUCHAR data = tlpBuffer + 12;
                        
                        printf("Memory Write 32: Address=0x%08x, Length=%d\n", mwr->Address, length);
                        
                        // Perform memory write
                        DWORD bytesWritten;
                        ProcessMemoryWrite(address, data, length, &bytesWritten);
                        
                    } else if (hdr->TypeFmt == TLP_MWr64) {
                        PTLP_HDR_MRdWr64 mwr = (PTLP_HDR_MRdWr64)tlpBuffer;
                        QWORD address = ((QWORD)mwr->AddressHigh << 32) | mwr->AddressLow;
                        DWORD length = hdr->Length ? (hdr->Length << 2) : 0x1000;
                        PUCHAR data = tlpBuffer + 16;
                        
                        printf("Memory Write 64: Address=0x%016llx, Length=%d\n", address, length);
                        
                        // Perform memory write
                        DWORD bytesWritten;
                        ProcessMemoryWrite(address, data, length, &bytesWritten);
                    }
                }
                
                // Reset for next TLP
                tlpLength = 0;
                tlpInProgress = FALSE;
            }
        } else if (dwControl == FPGA_LOOPBACK_TX && dwData == FPGA_LOOPBACK_DATA) {
            printf("Loopback packet detected\n");
        } else {
            printf("Unknown control word: 0x%08x\n", dwControl);
        }
        
        i += 8;  // Each FPGA protocol entry is 8 bytes (data + control)
    }
    
    return TRUE;
}

DWORD WINAPI ClientHandler(LPVOID lpParam)
{
    SOCKET clientSocket = (SOCKET)lpParam;
    UCHAR pipeID;
    ULONG bufferLength;
    PUCHAR buffer = NULL;
    
    printf("Client connected\n");
    
    while (TRUE) {
        // Receive pipe ID
        int result = recv(clientSocket, (char*)&pipeID, 1, 0);
        if (result <= 0) {
            break;
        }
        
        // Receive buffer length
        result = recv(clientSocket, (char*)&bufferLength, sizeof(bufferLength), 0);
        if (result <= 0) {
            break;
        }
        
        printf("Received request: PipeID=0x%02x, Length=%d\n", pipeID, bufferLength);
        
        if (pipeID == 0x02) {
            // TX pipe - receive FPGA protocol data and parse it
            buffer = (PUCHAR)malloc(bufferLength);
            if (!buffer) {
                break;
            }
            
            DWORD totalReceived = 0;
            while (totalReceived < bufferLength) {
                result = recv(clientSocket, (char*)buffer + totalReceived, bufferLength - totalReceived, 0);
                if (result <= 0) {
                    break;
                }
                totalReceived += result;
            }
            
            if (totalReceived == bufferLength) {
                // Parse the FPGA protocol
                ParseFPGAProtocol(buffer, bufferLength);
            }
            
            free(buffer);
            buffer = NULL;
            
        } else if (pipeID == 0x82) {
            // RX pipe - send back response data (for now, send dummy data)
            buffer = (PUCHAR)malloc(bufferLength);
            if (!buffer) {
                break;
            }
            
            // Fill with dummy data pattern for testing
            memset(buffer, 0x42, bufferLength);
            
            result = send(clientSocket, (char*)buffer, bufferLength, 0);
            
            free(buffer);
            buffer = NULL;
        }
    }
    
    printf("Client disconnected\n");
    closesocket(clientSocket);
    return 0;
}

int main() 
{
    WSADATA wsaData;
    SOCKET serverSocket, clientSocket;
    struct sockaddr_in serverAddr, clientAddr;
    int clientAddrLen = sizeof(clientAddr);
    
    printf("DMA Network Server - LeechCore FPGA Protocol Bridge\n");
    
    // Initialize Winsock
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        printf("WSAStartup failed\n");
        return 1;
    }
    
    // Create socket
    serverSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (serverSocket == INVALID_SOCKET) {
        printf("socket failed\n");
        WSACleanup();
        return 1;
    }
    
    // Setup server address
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_addr.s_addr = INADDR_ANY;
    serverAddr.sin_port = htons(DMA_SERVER_PORT);
    
    // Bind socket
    if (bind(serverSocket, (struct sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
        printf("bind failed\n");
        closesocket(serverSocket);
        WSACleanup();
        return 1;
    }
    
    // Listen for connections
    if (listen(serverSocket, MAX_CLIENTS) == SOCKET_ERROR) {
        printf("listen failed\n");
        closesocket(serverSocket);
        WSACleanup();
        return 1;
    }
    
    printf("Server listening on port %d\n", DMA_SERVER_PORT);
    
    // Accept connections
    while (TRUE) {
        clientSocket = accept(serverSocket, (struct sockaddr*)&clientAddr, &clientAddrLen);
        if (clientSocket == INVALID_SOCKET) {
            printf("accept failed\n");
            continue;
        }
        
        // Create thread to handle client
        HANDLE hThread = CreateThread(NULL, 0, ClientHandler, (LPVOID)clientSocket, 0, NULL);
        if (hThread) {
            CloseHandle(hThread);
        } else {
            closesocket(clientSocket);
        }
    }
    
    closesocket(serverSocket);
    WSACleanup();
    
    if (g_driverHandle != INVALID_HANDLE_VALUE) {
        CloseHandle(g_driverHandle);
    }
    
    return 0;
}