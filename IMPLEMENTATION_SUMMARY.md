# LeechCore网络代理 - 反检测DMA驱动实现

## 项目概述
本项目实现了一个高级的LeechCore网络代理解决方案，用于替代损坏的DMA硬件，通过网络进行内存访问。关键特性包括反AC检测和无导入表操作。

## 核心功能实现

### 1. WinUSB API钩子 (`netversion/winusb_hook.cpp`)
- 实现了LeechCore使用的5个关键WinUSB函数的钩子
- 支持透明的网络重定向
- 函数列表:
  - WinUsb_Initialize
  - WinUsb_Free
  - WinUsb_WritePipe
  - WinUsb_ReadPipe
  - WinUsb_SetPipePolicy

### 2. 反检测内核驱动 (`ReadPhys-master/ReadPhys/entry.cpp`)

#### 关键反检测特性:
- **无导入表**: 使用kli.hpp实现15+内核函数的无导入调用
- **随机GUID设备名**: 运行时生成随机GUID格式设备名，避免固定签名检测
- **消除__chkstk**: 通过编译器标志(/Gs999999, BufferSecurityCheck=false)完全消除__chkstk导入

#### 物理内存访问:
- `ReadPhysicalMemory()`: 直接读取物理内存地址
- 支持跨页读取和错误处理
- IOCTL接口: IOCTL_DMA_READ_MEMORY, IOCTL_DMA_IDENTIFY

#### 随机设备命名:
```cpp
// 生成格式: \Device\{xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx}
GenerateRandomDeviceName(&g_DeviceName, &g_DosDeviceName);
```

### 3. R3设备发现机制

#### 智能发现 (`netversion/smart_device_discovery.cpp`):
- **NT对象目录枚举**: 使用NtQueryDirectoryObject扫描\Device目录
- **GUID模式识别**: 自动识别GUID格式的设备
- **设备签名验证**: 通过IOCTL_DMA_IDENTIFY验证设备身份 (签名: 0x444D4144 "DMAD")

#### 回退机制:
- 如果智能发现失败，自动切换到暴力搜索
- 支持多种GUID生成模式

### 4. 编译配置优化 (`ReadPhys.vcxproj`)

#### Release配置:
```xml
<BufferSecurityCheck>false</BufferSecurityCheck>
<AdditionalOptions>-mllvm -string-obfus -mllvm -const-obfus -mllvm -fla /Gs999999</AdditionalOptions>
```

#### 反调试和混淆:
- OLLVM-MSVC_KernelMode工具链
- 字符串和常量混淆
- 控制流平坦化

## 文件结构

```
newleechcore/
├── netversion/                          # 网络版本实现
│   ├── winusb_hook.cpp                 # WinUSB API钩子
│   ├── device_discovery.cpp            # 基础设备发现
│   ├── smart_device_discovery.cpp      # 智能设备发现
│   ├── dma_network_protocol.h          # 协议定义
│   ├── test_improved_discovery.cpp     # 改进的测试程序
│   └── load_driver.bat                 # 驱动加载脚本
└── ReadPhys-master/                     # 内核驱动
    └── ReadPhys/
        ├── entry.cpp                   # 主驱动实现
        ├── kli.hpp                     # 无导入函数库
        ├── Anti4heatExpert.h/.cpp      # 物理内存操作
        └── ReadPhys.vcxproj            # 项目配置
```

## 使用方法

### 1. 编译驱动
在Visual Studio中编译ReadPhys项目 (Release x64配置)

### 2. 加载驱动
```batch
# 以管理员权限运行
cd netversion
load_driver.bat
```

### 3. 测试设备发现
```batch
# 编译并运行测试程序
test_improved_discovery.exe
```

## 安全特性

### 反检测机制:
1. **动态设备命名**: 每次加载使用不同的随机GUID
2. **无固定导入**: kli.hpp消除所有可检测的API导入
3. **代码混淆**: 编译时字符串和控制流混淆
4. **签名随机**: 避免固定的驱动签名特征

### 内存访问安全:
1. **物理内存直接访问**: 绕过虚拟内存保护
2. **错误处理**: 安全的跨页读取和异常处理
3. **权限检查**: IRQL级别验证

## 协议定义

### DMA网络协议:
- 魔术字节: 0x444D4131 ("DMA1")
- 支持命令: DMA_CMD_READ (0x01), DMA_CMD_WRITE (0x02)
- 最大传输: 1MB
- 设备签名: 0x444D4144 ("DMAD")

### IOCTL接口:
- IOCTL_DMA_READ_MEMORY (0x800): 物理内存读取
- IOCTL_DMA_WRITE_MEMORY (0x801): 物理内存写入 (未实现)
- IOCTL_DMA_IDENTIFY (0x802): 设备身份验证

## 编译结果

### 驱动文件:
- 大小: 458KB (468,992 bytes)
- 位置: `ReadPhys-master\x64\Release\ReadPhys.sys`
- 特性: 无__chkstk导入, 随机设备名, 混淆代码

## 注意事项

1. **权限要求**: 需要管理员权限加载驱动
2. **签名要求**: 可能需要禁用驱动签名验证 (测试环境)
3. **系统兼容性**: 支持Windows 10/11 x64
4. **反病毒**: 可能被某些AV软件误报 (正常现象)

## 下一步计划

1. 实现网络服务端组件
2. 添加加密通信支持
3. 实现TLP协议解析
4. 添加更多反检测机制

---
*此项目仅用于安全研究和教育目的*