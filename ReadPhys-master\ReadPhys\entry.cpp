锘�#include <ntifs.h>
#include "Anti4heatExpert.h"
#include "kli.hpp"
#include <ntstrsafe.h>

// 鑷畾涔� __chkstk 瀹炵幇浠ラ伩鍏嶅鍏�
extern "C" void __chkstk(void)
{
    // 绌哄疄鐜� - 鎴戜滑宸茬粡閫氳繃 /Gs65536 璁剧疆浜嗚冻澶熷ぇ鐨勬爤淇濇姢
}

// 绂佺敤寮傚父澶勭悊鐩稿叧鐨勭鍙�
extern "C" int _fltused = 0;

// 鍐呮牳椹卞姩DMA鍗忚瀹氫箟 - 閬垮厤鍖呭惈鐢ㄦ埛妯″紡澶存枃浠�
#define DMA_MAGIC 0x444D4131  // "DMA1"
#define DMA_CMD_READ  0x01
#define DMA_CMD_WRITE 0x02

// 椹卞姩IOCTL瀹氫箟
#define IOCTL_DMA_READ_MEMORY   CTL_CODE(FILE_DEVICE_UNKNOWN, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_DMA_WRITE_MEMORY  CTL_CODE(FILE_DEVICE_UNKNOWN, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_DMA_IDENTIFY      CTL_CODE(FILE_DEVICE_UNKNOWN, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS)

// 璁惧璇嗗埆鏍囪瘑绗�
#define DMA_DEVICE_SIGNATURE    0x444D4144  // "DMAD"

// 椹卞姩璇锋眰缁撴瀯
typedef struct _DMA_REQUEST {
    DWORD Command;      // DMA_CMD_READ 鎴� DMA_CMD_WRITE
    ULONG64 Address;    // 鐗╃悊鍐呭瓨鍦板潃
    DWORD Length;       // 鏁版嵁闀垮害
    DWORD Reserved;     // 淇濈暀瀛楁
} DMA_REQUEST, *PDMA_REQUEST;

// 瀹氫箟缂撳瓨鐨刱li鍑芥暟鎸囬拡
KLI_CACHED_DEF(KeGetCurrentIrql);
KLI_CACHED_DEF(DbgPrint);
KLI_CACHED_DEF(ExAllocatePool2);
KLI_CACHED_DEF(ExFreePool);
KLI_CACHED_DEF(IoCreateDevice);
KLI_CACHED_DEF(IoCreateSymbolicLink);
KLI_CACHED_DEF(IoDeleteDevice);
KLI_CACHED_DEF(IoDeleteSymbolicLink);
KLI_CACHED_DEF(IofCompleteRequest);
KLI_CACHED_DEF(RtlInitUnicodeString);
KLI_CACHED_DEF(RtlStringCchPrintfW);
KLI_CACHED_DEF(KeQueryTimeIncrement);

// 鍏ㄥ眬鍙橀噺淇濆瓨璁惧鍚嶇О
UNICODE_STRING g_DeviceName;
UNICODE_STRING g_DosDeviceName;

// 绠€鍗曠殑瀛楃涓茶В娣锋穯鍑芥暟
VOID DeobfuscateString(PWCHAR dest, const UCHAR* src, ULONG len, UCHAR key)
{
    for (ULONG i = 0; i < len; i++) {
        dest[i] = (WCHAR)(src[i] ^ key);
    }
    dest[len] = L'\0';
}

// 娣锋穯鐨勫瓧绗︿覆鏁版嵁
static const UCHAR g_DeviceFormat[] = {
    0x6F^0xAA, 0x00^0xAA, 0x44^0xAA, 0x00^0xAA, 0x65^0xAA, 0x00^0xAA, 0x76^0xAA, 0x00^0xAA, 0x69^0xAA, 0x00^0xAA, 0x63^0xAA, 0x00^0xAA, 0x65^0xAA, 0x00^0xAA, 0x6F^0xAA, 0x00^0xAA,
    0x6F^0xAA, 0x00^0xAA, 0x7B^0xAA, 0x00^0xAA, 0x25^0xAA, 0x00^0xAA, 0x30^0xAA, 0x00^0xAA, 0x38^0xAA, 0x00^0xAA, 0x78^0xAA, 0x00^0xAA, 0x2D^0xAA, 0x00^0xAA, 0x25^0xAA, 0x00^0xAA,
    0x30^0xAA, 0x00^0xAA, 0x34^0xAA, 0x00^0xAA, 0x78^0xAA, 0x00^0xAA, 0x2D^0xAA, 0x00^0xAA, 0x25^0xAA, 0x00^0xAA, 0x30^0xAA, 0x00^0xAA, 0x34^0xAA, 0x00^0xAA, 0x78^0xAA, 0x00^0xAA,
    0x2D^0xAA, 0x00^0xAA, 0x25^0xAA, 0x00^0xAA, 0x30^0xAA, 0x00^0xAA, 0x34^0xAA, 0x00^0xAA, 0x78^0xAA, 0x00^0xAA, 0x2D^0xAA, 0x00^0xAA, 0x25^0xAA, 0x00^0xAA, 0x30^0xAA, 0x00^0xAA,
    0x38^0xAA, 0x00^0xAA, 0x78^0xAA, 0x00^0xAA, 0x25^0xAA, 0x00^0xAA, 0x30^0xAA, 0x00^0xAA, 0x34^0xAA, 0x00^0xAA, 0x78^0xAA, 0x00^0xAA, 0x7D^0xAA, 0x00^0xAA
};

static const UCHAR g_DosFormat[] = {
    0x6F^0xBB, 0x00^0xBB, 0x44^0xBB, 0x00^0xBB, 0x6F^0xBB, 0x00^0xBB, 0x73^0xBB, 0x00^0xBB, 0x44^0xBB, 0x00^0xBB, 0x65^0xBB, 0x00^0xBB, 0x76^0xBB, 0x00^0xBB, 0x69^0xBB, 0x00^0xBB,
    0x63^0xBB, 0x00^0xBB, 0x65^0xBB, 0x00^0xBB, 0x73^0xBB, 0x00^0xBB, 0x6F^0xBB, 0x00^0xBB, 0x7B^0xBB, 0x00^0xBB, 0x25^0xBB, 0x00^0xBB, 0x30^0xBB, 0x00^0xBB, 0x38^0xBB, 0x00^0xBB,
    0x78^0xBB, 0x00^0xBB, 0x2D^0xBB, 0x00^0xBB, 0x25^0xBB, 0x00^0xBB, 0x30^0xBB, 0x00^0xBB, 0x34^0xBB, 0x00^0xBB, 0x78^0xBB, 0x00^0xBB, 0x2D^0xBB, 0x00^0xBB, 0x25^0xBB, 0x00^0xBB,
    0x30^0xBB, 0x00^0xBB, 0x34^0xBB, 0x00^0xBB, 0x78^0xBB, 0x00^0xBB, 0x2D^0xBB, 0x00^0xBB, 0x25^0xBB, 0x00^0xBB, 0x30^0xBB, 0x00^0xBB, 0x34^0xBB, 0x00^0xBB, 0x78^0xBB, 0x00^0xBB,
    0x2D^0xBB, 0x00^0xBB, 0x25^0xBB, 0x00^0xBB, 0x30^0xBB, 0x00^0xBB, 0x38^0xBB, 0x00^0xBB, 0x78^0xBB, 0x00^0xBB, 0x25^0xBB, 0x00^0xBB, 0x30^0xBB, 0x00^0xBB, 0x34^0xBB, 0x00^0xBB,
    0x78^0xBB, 0x00^0xBB, 0x7D^0xBB, 0x00^0xBB
};

// 鍩轰簬鍥哄畾绠楁硶鐨凣UID鐢熸垚鍣� - R3绋嬪簭鍙互浣跨敤鐩稿悓绠楁硶
ULONG SimpleHash(const char* str, ULONG seed)
{
    ULONG hash = seed;
    while (*str) {
        hash = hash * 33 + (ULONG)(*str);
        str++;
    }
    return hash;
}

// 鐢熸垚鍙娴嬬殑GUID缁勪欢
VOID GeneratePredictableGUID(ULONG* part1, ULONG* part2, ULONG* part3, ULONG* part4)
{
    // 浣跨敤鍥哄畾鐨勭瀛愬瓧绗︿覆锛孯3绋嬪簭鍙互浣跨敤鐩稿悓鐨勫瓧绗︿覆
    const char* seed1 = "ReadPhysDevice2024";
    const char* seed2 = "DMAController";
    const char* seed3 = "PhysicalMemory";
    const char* seed4 = "KernelDriver";

    *part1 = SimpleHash(seed1, 0x12345678);
    *part2 = SimpleHash(seed2, 0x87654321);
    *part3 = SimpleHash(seed3, 0xABCDEF00);
    *part4 = SimpleHash(seed4, 0x00FEDCBA);
}

// 鐢熸垚闅忔満GUID鏍煎紡鐨勮澶囧悕绉�
NTSTATUS GenerateRandomDeviceName(PUNICODE_STRING DeviceName, PUNICODE_STRING DosDeviceName)
{
    NTSTATUS status;
    PWCHAR deviceBuffer = NULL;
    PWCHAR dosBuffer = NULL;
    PWCHAR formatBuffer = NULL;
    
    // 鍒嗛厤璁惧鍚嶇О缂撳啿鍖�
    deviceBuffer = (PWCHAR)KLI_CACHED_CALL(ExAllocatePool2, POOL_FLAG_NON_PAGED, 256, 'maDN');
    if (!deviceBuffer) {
        return STATUS_INSUFFICIENT_RESOURCES;
    }
    
    dosBuffer = (PWCHAR)KLI_CACHED_CALL(ExAllocatePool2, POOL_FLAG_NON_PAGED, 256, 'maDN');
    if (!dosBuffer) {
        KLI_CACHED_CALL(ExFreePool, deviceBuffer);
        return STATUS_INSUFFICIENT_RESOURCES;
    }
    
    formatBuffer = (PWCHAR)KLI_CACHED_CALL(ExAllocatePool2, POOL_FLAG_NON_PAGED, 128, 'tmtF');
    if (!formatBuffer) {
        KLI_CACHED_CALL(ExFreePool, deviceBuffer);
        KLI_CACHED_CALL(ExFreePool, dosBuffer);
        return STATUS_INSUFFICIENT_RESOURCES;
    }
    
    // 鐢熸垚鍙娴嬬殑GUID缁勪欢 - R3绋嬪簭鍙互浣跨敤鐩稿悓绠楁硶
    ULONG part1, part2, part3, part4;
    GeneratePredictableGUID(&part1, &part2, &part3, &part4);
    
    // 瑙ｆ贩娣嗚澶囨牸寮忓瓧绗︿覆
    DeobfuscateString(formatBuffer, g_DeviceFormat, sizeof(g_DeviceFormat)/2, 0xAA);
    
    // 鏋勫缓璁惧鍚嶇О (绫讳技浜庡悎娉曠殑璁惧GUID鏍煎紡)
    status = KLI_CACHED_CALL(RtlStringCchPrintfW, deviceBuffer, 128,
        formatBuffer,
        part1,
        (USHORT)(part2 >> 16),
        (USHORT)(part2 & 0xFFFF),
        (USHORT)(part3 >> 16),
        part4,
        (USHORT)(part3 & 0xFFFF));
    
    if (!NT_SUCCESS(status)) {
        KLI_CACHED_CALL(ExFreePool, deviceBuffer);
        KLI_CACHED_CALL(ExFreePool, dosBuffer);
        KLI_CACHED_CALL(ExFreePool, formatBuffer);
        return status;
    }
    
    // 瑙ｆ贩娣咲OS鏍煎紡瀛楃涓�
    DeobfuscateString(formatBuffer, g_DosFormat, sizeof(g_DosFormat)/2, 0xBB);
    
    // 鏋勫缓DOS璁惧鍚嶇О
    status = KLI_CACHED_CALL(RtlStringCchPrintfW, dosBuffer, 128,
        formatBuffer,
        part1,
        (USHORT)(part2 >> 16),
        (USHORT)(part2 & 0xFFFF),
        (USHORT)(part3 >> 16),
        part4,
        (USHORT)(part3 & 0xFFFF));
    
    if (!NT_SUCCESS(status)) {
        KLI_CACHED_CALL(ExFreePool, deviceBuffer);
        KLI_CACHED_CALL(ExFreePool, dosBuffer);
        KLI_CACHED_CALL(ExFreePool, formatBuffer);
        return status;
    }
    
    KLI_CACHED_CALL(RtlInitUnicodeString, DeviceName, deviceBuffer);
    KLI_CACHED_CALL(RtlInitUnicodeString, DosDeviceName, dosBuffer);
    
    // 娓呴浂骞堕噴鏀炬牸寮忕紦鍐插尯
    memset(formatBuffer, 0, 128);
    KLI_CACHED_CALL(ExFreePool, formatBuffer);
    
    return STATUS_SUCCESS;
}




ULONG ReadVirtualMemory(ULONG64 DirectoryTableBase, PVOID Address, PVOID Buffer, PULONG pSizeRead)
{
	BOOLEAN IsDoneRead;
	ULONG ErrorCode;
	ULONG SizeLeft;
	ULONG OffsetInPage;
	ULONG SizeRead;
	ULONG SizeLeftInPage;
	ULONG Size;
	ULONG64 PhysicalAddress;
	ULONG64 PageSize;
	ULONG64 PageAddress;
	Anti4heatExpert::PHYSICAL_PAGE_INFO TransferPageInfo;

	SizeRead = 0;
	memset(&TransferPageInfo, 0, sizeof(TransferPageInfo));
	if (!DirectoryTableBase)
	{
		DirectoryTableBase = __readcr3();
	}
	if (Address && Buffer && pSizeRead && *pSizeRead)
	{
		if (KLI_CACHED_CALL(KeGetCurrentIrql) <= 2u)
		{
			Anti4heatExpert::AllocatePhysicalPage(&TransferPageInfo, 0x1000);
			PageSize = 0;
			ErrorCode = Anti4heatExpert::GetPhysPageSize(&TransferPageInfo, (ULONG64)Address, &PageSize, DirectoryTableBase);
			if (!ErrorCode && PageSize > 0x1000)
			{
				ErrorCode = 1;
				goto _EXIT;
			}
			OffsetInPage = (ULONG64)Address & 0xFFF;
			PageAddress = (ULONG64)Address & 0xFFFFFFFFFFFFF000u;
			Size = *pSizeRead;
			SizeLeft = *pSizeRead;
			do
			{
				IsDoneRead = FALSE;
				if (SizeLeft >= PAGE_SIZE - OffsetInPage)
					SizeLeftInPage = PAGE_SIZE - OffsetInPage;
				else
					SizeLeftInPage = SizeLeft;

				PhysicalAddress = 0;
				ErrorCode = Anti4heatExpert::GetPhysPageAddress(&TransferPageInfo, DirectoryTableBase, (PVOID)PageAddress, &PhysicalAddress);

				if (!ErrorCode && PhysicalAddress
					&& !Anti4heatExpert::ReadPhysicalPage(
						&TransferPageInfo,
						PhysicalAddress + OffsetInPage,
						Buffer,
						SizeLeftInPage))
				{
					SizeRead += SizeLeftInPage;
					IsDoneRead = TRUE;
				}
				if (!IsDoneRead)
					memset(Buffer, 0, SizeLeftInPage);
				Buffer = (PUCHAR)Buffer + SizeLeftInPage;
				PageAddress += OffsetInPage + (ULONG64)SizeLeftInPage;
				OffsetInPage = 0;
				SizeLeft -= SizeLeftInPage;
			} while (SizeLeft && SizeLeft < Size);
			if (SizeRead)
			{
				*pSizeRead = SizeRead;
				ErrorCode = 0;
			}
			else
			{
				ErrorCode = 216;
			}
		}
		else
		{
			ErrorCode = 261;
		}
	}
	else
	{
		ErrorCode = 22;
	}
	_EXIT:
	Anti4heatExpert::FreePhysicalPage(&TransferPageInfo);
	return ErrorCode;
}

// 直锟接讹拷取锟斤拷锟斤拷锟节达拷锟街凤拷锟斤拷锟�
ULONG ReadPhysicalMemory(ULONG64 PhysicalAddress, PVOID Buffer, ULONG Size, PULONG pSizeRead)
{
	ULONG ErrorCode;
	ULONG SizeRead = 0;
	ULONG SizeLeft = Size;
	ULONG SizeLeftInPage;
	ULONG64 PhysPageBase;
	ULONG OffsetInPage;
	Anti4heatExpert::PHYSICAL_PAGE_INFO TransferPageInfo;

	memset(&TransferPageInfo, 0, sizeof(TransferPageInfo));
	
	if (!Buffer || !pSizeRead || !Size)
	{
		return 22; // ERROR_INVALID_PARAMETER
	}

	*pSizeRead = 0;

	if (KLI_CACHED_CALL(KeGetCurrentIrql) > 2u)
	{
		return 261; // ERROR_INVALID_LEVEL
	}

	// 锟斤拷锟斤拷锟斤拷锟斤拷页锟斤拷锟节达拷锟斤拷
	ErrorCode = Anti4heatExpert::AllocatePhysicalPage(&TransferPageInfo, 0x1000);
	if (ErrorCode)
	{
		return ErrorCode;
	}

	while (SizeLeft > 0)
	{
		PhysPageBase = PhysicalAddress & 0xFFFFFFFFFFFFF000ULL; // 页锟斤拷锟斤拷
		OffsetInPage = (ULONG)(PhysicalAddress & 0xFFF);
		
		// 锟斤拷锟姐本锟轿讹拷取锟斤拷小
		if (SizeLeft >= PAGE_SIZE - OffsetInPage)
			SizeLeftInPage = PAGE_SIZE - OffsetInPage;
		else
			SizeLeftInPage = SizeLeft;

		// 锟斤拷取锟斤拷锟斤拷页
		ErrorCode = Anti4heatExpert::ReadPhysicalPage(
			&TransferPageInfo,
			PhysPageBase + OffsetInPage,
			(PUCHAR)Buffer + SizeRead,
			SizeLeftInPage);

		if (ErrorCode)
		{
			// 锟斤拷取失锟杰ｏ拷锟斤拷锟�0
			memset((PUCHAR)Buffer + SizeRead, 0, SizeLeftInPage);
		}
		else
		{
			SizeRead += SizeLeftInPage;
		}

		PhysicalAddress += SizeLeftInPage;
		SizeLeft -= SizeLeftInPage;
	}

	Anti4heatExpert::FreePhysicalPage(&TransferPageInfo);
	
	*pSizeRead = SizeRead;
	return SizeRead > 0 ? 0 : 216; // ERROR_ARITHMETIC_OVERFLOW if no data read
}

// IOCTL锟街凤拷锟斤拷锟斤拷
NTSTATUS DmaDeviceControl(PDEVICE_OBJECT DeviceObject, PIRP Irp)
{
	UNREFERENCED_PARAMETER(DeviceObject);
	
	PIO_STACK_LOCATION irpStack = IoGetCurrentIrpStackLocation(Irp);
	NTSTATUS status = STATUS_INVALID_DEVICE_REQUEST;
	ULONG bytesReturned = 0;
	
	switch (irpStack->Parameters.DeviceIoControl.IoControlCode)
	{
		case IOCTL_DMA_READ_MEMORY:
		{
			PDMA_REQUEST request = (PDMA_REQUEST)Irp->AssociatedIrp.SystemBuffer;
			PVOID outputBuffer = Irp->AssociatedIrp.SystemBuffer;
			ULONG inputLength = irpStack->Parameters.DeviceIoControl.InputBufferLength;
			ULONG outputLength = irpStack->Parameters.DeviceIoControl.OutputBufferLength;
			
			if (inputLength >= sizeof(DMA_REQUEST) && 
				outputLength >= request->Length &&
				request->Command == DMA_CMD_READ)
			{
				ULONG bytesRead = 0;
				ULONG errorCode = ReadPhysicalMemory(
					request->Address,
					outputBuffer,
					request->Length,
					&bytesRead);
					
				if (errorCode == 0)
				{
					bytesReturned = bytesRead;
					status = STATUS_SUCCESS;
				}
				else
				{
					status = STATUS_UNSUCCESSFUL;
				}
			}
			else
			{
				status = STATUS_INVALID_PARAMETER;
			}
			break;
		}
		
		case IOCTL_DMA_WRITE_MEMORY:
		{
			// 鏆傛椂涓嶅疄鐜板啓鍏ュ姛鑳�
			status = STATUS_NOT_IMPLEMENTED;
			break;
		}
		
		case IOCTL_DMA_IDENTIFY:
		{
			// 璁惧璇嗗埆璇锋眰
			ULONG outputLength = irpStack->Parameters.DeviceIoControl.OutputBufferLength;
			if (outputLength >= sizeof(DWORD)) {
				*(DWORD*)Irp->AssociatedIrp.SystemBuffer = DMA_DEVICE_SIGNATURE;
				bytesReturned = sizeof(DWORD);
				status = STATUS_SUCCESS;
			} else {
				status = STATUS_INVALID_PARAMETER;
			}
			break;
		}
		
		default:
			status = STATUS_INVALID_DEVICE_REQUEST;
			break;
	}
	
	Irp->IoStatus.Status = status;
	Irp->IoStatus.Information = bytesReturned;
	KLI_CACHED_CALL(IofCompleteRequest, Irp, IO_NO_INCREMENT);
	
	return status;
}

// 通锟矫分凤拷锟斤拷锟斤拷
NTSTATUS DmaDispatchCreateClose(PDEVICE_OBJECT DeviceObject, PIRP Irp)
{
	UNREFERENCED_PARAMETER(DeviceObject);
	
	Irp->IoStatus.Status = STATUS_SUCCESS;
	Irp->IoStatus.Information = 0;
	KLI_CACHED_CALL(IofCompleteRequest,Irp, IO_NO_INCREMENT);
	
	return STATUS_SUCCESS;
}


EXTERN_C NTSTATUS DriverEntry(PDRIVER_OBJECT DriverObject, PUNICODE_STRING RegistryPath)
{
	UNREFERENCED_PARAMETER(RegistryPath);

	NTSTATUS status;
	PDEVICE_OBJECT deviceObject = NULL;

	// 鍒濆鍖杒li缂撳瓨鐨勫嚱鏁版寚閽�
	KLI_CACHED_SET(KeGetCurrentIrql);
	KLI_CACHED_SET(DbgPrint);
	KLI_CACHED_SET(ExAllocatePool2);
	KLI_CACHED_SET(ExFreePool);
	KLI_CACHED_SET(IoCreateDevice);
	KLI_CACHED_SET(IoCreateSymbolicLink);
	KLI_CACHED_SET(IoDeleteDevice);
	KLI_CACHED_SET(IoDeleteSymbolicLink);
	KLI_CACHED_SET(IofCompleteRequest);
	KLI_CACHED_SET(RtlInitUnicodeString);
	KLI_CACHED_SET(RtlStringCchPrintfW);
	KLI_CACHED_SET(KeQueryTimeIncrement);
	
	// 鍒濆鍖朅nti4heatExpert鐨刱li缂撳瓨
	Anti4heatExpert::InitializeKliCache();

	// 鐢熸垚闅忔満GUID鏍煎紡鐨勮澶囧悕绉�
	status = GenerateRandomDeviceName(&g_DeviceName, &g_DosDeviceName);
	if (!NT_SUCCESS(status)) {
		KLI_CACHED_CALL(DbgPrint, "[!] Failed to generate random device name: 0x%08X\n", status);
		return status;
	}

	KLI_CACHED_CALL(DbgPrint, "[+] Generated device name: %wZ\n", &g_DeviceName);

	// 鍒涘缓璁惧瀵硅薄
	status = KLI_CACHED_CALL(IoCreateDevice,
		DriverObject,
		0,
		&g_DeviceName,
		FILE_DEVICE_UNKNOWN,
		FILE_DEVICE_SECURE_OPEN,
		FALSE,
		&deviceObject);

	if (!NT_SUCCESS(status))
	{
		KLI_CACHED_CALL(DbgPrint, "[!] Failed to create device object: 0x%08X\n", status);
		// 娓呯悊鍒嗛厤鐨勭紦鍐插尯
		if (g_DeviceName.Buffer) KLI_CACHED_CALL(ExFreePool, g_DeviceName.Buffer);
		if (g_DosDeviceName.Buffer) KLI_CACHED_CALL(ExFreePool, g_DosDeviceName.Buffer);
		return status;
	}

	// 鍒涘缓绗﹀彿閾炬帴
	status = KLI_CACHED_CALL(IoCreateSymbolicLink, &g_DosDeviceName, &g_DeviceName);
	if (!NT_SUCCESS(status))
	{
		KLI_CACHED_CALL(DbgPrint, "[!] Failed to create symbolic link: 0x%08X\n", status);
		KLI_CACHED_CALL(IoDeleteDevice, deviceObject);
		// 娓呯悊鍒嗛厤鐨勭紦鍐插尯
		if (g_DeviceName.Buffer) KLI_CACHED_CALL(ExFreePool, g_DeviceName.Buffer);
		if (g_DosDeviceName.Buffer) KLI_CACHED_CALL(ExFreePool, g_DosDeviceName.Buffer);
		return status;
	}

	// 锟斤拷锟矫分凤拷锟斤拷锟斤拷
	DriverObject->MajorFunction[IRP_MJ_CREATE] = DmaDispatchCreateClose;
	DriverObject->MajorFunction[IRP_MJ_CLOSE] = DmaDispatchCreateClose;
	DriverObject->MajorFunction[IRP_MJ_DEVICE_CONTROL] = DmaDeviceControl;

	DriverObject->DriverUnload = [](PDRIVER_OBJECT DriverObject)->VOID {
		// 鍒犻櫎绗﹀彿閾炬帴鍜岃澶囧璞�
		if (g_DosDeviceName.Buffer) {
			KLI_CACHED_CALL(IoDeleteSymbolicLink, &g_DosDeviceName);
			KLI_CACHED_CALL(ExFreePool, g_DosDeviceName.Buffer);
		}
		
		if (DriverObject->DeviceObject)
		{
			KLI_CACHED_CALL(IoDeleteDevice, DriverObject->DeviceObject);
		}
		
		// 娓呯悊璁惧鍚嶇О缂撳啿鍖�
		if (g_DeviceName.Buffer) {
			KLI_CACHED_CALL(ExFreePool, g_DeviceName.Buffer);
		}
		
		KLI_CACHED_CALL(DbgPrint, "[+] DMA Driver unloaded\n");
	};

	// 锟斤拷锟斤拷锟借备锟斤拷志
	deviceObject->Flags |= DO_BUFFERED_IO;
	deviceObject->Flags &= ~DO_DEVICE_INITIALIZING;

	KLI_CACHED_CALL(DbgPrint, "[+] DMA Driver loaded successfully\n");
	
	return STATUS_SUCCESS;
}

