锘块敇锟�#include <ntifs.h>
#include "Anti4heatExpert.h"
#include "kli.hpp"
#include <ntstrsafe.h>

// 閼奉亜鐣炬稊锟� __chkstk 鐎圭偟骞囨禒銉╀缉閸忓秴顕遍崗锟�
extern "C" void __chkstk(void)
{
    // 缁屽搫鐤勯悳锟� - 閹存垳婊戝鑼病闁俺绻� /Gs65536 鐠佸墽鐤嗘禍鍡氬喕婢剁喎銇囬惃鍕垽娣囨繃濮�
}

// 缁備胶鏁ゅ鍌氱埗婢跺嫮鎮婇惄绋垮彠閻ㄥ嫮顑侀崣锟�
extern "C" int _fltused = 0;

// 閸愬懏鐗虫す鍗炲ЗDMA閸楀繗顔呯€规矮绠� - 闁灝鍘ら崠鍛儓閻€劍鍩涘Ο鈥崇础婢跺瓨鏋冩禒锟�
#define DMA_MAGIC 0x444D4131  // "DMA1"
#define DMA_CMD_READ  0x01
#define DMA_CMD_WRITE 0x02

// 妞瑰崬濮㊣OCTL鐎规矮绠�
#define IOCTL_DMA_READ_MEMORY   CTL_CODE(FILE_DEVICE_UNKNOWN, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_DMA_WRITE_MEMORY  CTL_CODE(FILE_DEVICE_UNKNOWN, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_DMA_IDENTIFY      CTL_CODE(FILE_DEVICE_UNKNOWN, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS)

// 鐠佹儳顦拠鍡楀焼閺嶅洩鐦戠粭锟�
#define DMA_DEVICE_SIGNATURE    0x444D4144  // "DMAD"

// 妞瑰崬濮╃拠閿嬬湴缂佹挻鐎�
typedef struct _DMA_REQUEST {
    DWORD Command;      // DMA_CMD_READ 閹达拷 DMA_CMD_WRITE
    ULONG64 Address;    // 閻椻晝鎮婇崘鍛摠閸︽澘娼�
    DWORD Length;       // 閺佺増宓侀梹鍨
    DWORD Reserved;     // 娣囨繄鏆€鐎涙顔�
} DMA_REQUEST, *PDMA_REQUEST;

// 鐎规矮绠熺紓鎾崇摠閻ㄥ埍li閸戣姤鏆熼幐鍥嫛
KLI_CACHED_DEF(KeGetCurrentIrql);
KLI_CACHED_DEF(DbgPrint);
KLI_CACHED_DEF(ExAllocatePool2);
KLI_CACHED_DEF(ExFreePool);
KLI_CACHED_DEF(IoCreateDevice);
KLI_CACHED_DEF(IoCreateSymbolicLink);
KLI_CACHED_DEF(IoDeleteDevice);
KLI_CACHED_DEF(IoDeleteSymbolicLink);
KLI_CACHED_DEF(IofCompleteRequest);
KLI_CACHED_DEF(RtlInitUnicodeString);
KLI_CACHED_DEF(RtlStringCchPrintfW);
KLI_CACHED_DEF(KeQueryTimeIncrement);

// 閸忋劌鐪崣姗€鍣烘穱婵嗙摠鐠佹儳顦崥宥囆�
UNICODE_STRING g_DeviceName;
UNICODE_STRING g_DosDeviceName;

// 缁犫偓閸楁洜娈戠€涙顑佹稉鑼缎掑ǎ閿嬬┋閸戣姤鏆�
VOID DeobfuscateString(PWCHAR dest, const UCHAR* src, ULONG len, UCHAR key)
{
    for (ULONG i = 0; i < len; i++) {
        dest[i] = (WCHAR)(src[i] ^ key);
    }
    dest[len] = L'\0';
}

// 濞ｉ攱绌惃鍕摟缁楋缚瑕嗛弫鐗堝祦
static const UCHAR g_DeviceFormat[] = {
    0x6F^0xAA, 0x00^0xAA, 0x44^0xAA, 0x00^0xAA, 0x65^0xAA, 0x00^0xAA, 0x76^0xAA, 0x00^0xAA, 0x69^0xAA, 0x00^0xAA, 0x63^0xAA, 0x00^0xAA, 0x65^0xAA, 0x00^0xAA, 0x6F^0xAA, 0x00^0xAA,
    0x6F^0xAA, 0x00^0xAA, 0x7B^0xAA, 0x00^0xAA, 0x25^0xAA, 0x00^0xAA, 0x30^0xAA, 0x00^0xAA, 0x38^0xAA, 0x00^0xAA, 0x78^0xAA, 0x00^0xAA, 0x2D^0xAA, 0x00^0xAA, 0x25^0xAA, 0x00^0xAA,
    0x30^0xAA, 0x00^0xAA, 0x34^0xAA, 0x00^0xAA, 0x78^0xAA, 0x00^0xAA, 0x2D^0xAA, 0x00^0xAA, 0x25^0xAA, 0x00^0xAA, 0x30^0xAA, 0x00^0xAA, 0x34^0xAA, 0x00^0xAA, 0x78^0xAA, 0x00^0xAA,
    0x2D^0xAA, 0x00^0xAA, 0x25^0xAA, 0x00^0xAA, 0x30^0xAA, 0x00^0xAA, 0x34^0xAA, 0x00^0xAA, 0x78^0xAA, 0x00^0xAA, 0x2D^0xAA, 0x00^0xAA, 0x25^0xAA, 0x00^0xAA, 0x30^0xAA, 0x00^0xAA,
    0x38^0xAA, 0x00^0xAA, 0x78^0xAA, 0x00^0xAA, 0x25^0xAA, 0x00^0xAA, 0x30^0xAA, 0x00^0xAA, 0x34^0xAA, 0x00^0xAA, 0x78^0xAA, 0x00^0xAA, 0x7D^0xAA, 0x00^0xAA
};

static const UCHAR g_DosFormat[] = {
    0x6F^0xBB, 0x00^0xBB, 0x44^0xBB, 0x00^0xBB, 0x6F^0xBB, 0x00^0xBB, 0x73^0xBB, 0x00^0xBB, 0x44^0xBB, 0x00^0xBB, 0x65^0xBB, 0x00^0xBB, 0x76^0xBB, 0x00^0xBB, 0x69^0xBB, 0x00^0xBB,
    0x63^0xBB, 0x00^0xBB, 0x65^0xBB, 0x00^0xBB, 0x73^0xBB, 0x00^0xBB, 0x6F^0xBB, 0x00^0xBB, 0x7B^0xBB, 0x00^0xBB, 0x25^0xBB, 0x00^0xBB, 0x30^0xBB, 0x00^0xBB, 0x38^0xBB, 0x00^0xBB,
    0x78^0xBB, 0x00^0xBB, 0x2D^0xBB, 0x00^0xBB, 0x25^0xBB, 0x00^0xBB, 0x30^0xBB, 0x00^0xBB, 0x34^0xBB, 0x00^0xBB, 0x78^0xBB, 0x00^0xBB, 0x2D^0xBB, 0x00^0xBB, 0x25^0xBB, 0x00^0xBB,
    0x30^0xBB, 0x00^0xBB, 0x34^0xBB, 0x00^0xBB, 0x78^0xBB, 0x00^0xBB, 0x2D^0xBB, 0x00^0xBB, 0x25^0xBB, 0x00^0xBB, 0x30^0xBB, 0x00^0xBB, 0x34^0xBB, 0x00^0xBB, 0x78^0xBB, 0x00^0xBB,
    0x2D^0xBB, 0x00^0xBB, 0x25^0xBB, 0x00^0xBB, 0x30^0xBB, 0x00^0xBB, 0x38^0xBB, 0x00^0xBB, 0x78^0xBB, 0x00^0xBB, 0x25^0xBB, 0x00^0xBB, 0x30^0xBB, 0x00^0xBB, 0x34^0xBB, 0x00^0xBB,
    0x78^0xBB, 0x00^0xBB, 0x7D^0xBB, 0x00^0xBB
};

// 閸╄桨绨崶鍝勭暰缁犳纭堕惃鍑ID閻㈢喐鍨氶崳锟� - R3缁嬪绨崣顖欎簰娴ｈ法鏁ら惄绋挎倱缁犳纭�
ULONG SimpleHash(const char* str, ULONG seed)
{
    ULONG hash = seed;
    while (*str) {
        hash = hash * 33 + (ULONG)(*str);
        str++;
    }
    return hash;
}

// 閻㈢喐鍨氶崣顖烆暕濞村娈慓UID缂佸嫪娆�
VOID GeneratePredictableGUID(ULONG* part1, ULONG* part2, ULONG* part3, ULONG* part4)
{
    // 娴ｈ法鏁ら崶鍝勭暰閻ㄥ嫮顫掔€涙劕鐡х粭锔胯閿涘3缁嬪绨崣顖欎簰娴ｈ法鏁ら惄绋挎倱閻ㄥ嫬鐡х粭锔胯
    // 鑾峰彇CPU ID浣滀负绯荤粺鍞竴鏍囪瘑
    int cpuInfo[4];
    __cpuid(cpuInfo, 1);
    ULONG cpuId = (ULONG)cpuInfo[3]; // EDX瀵勫瓨鍣�

    // 鍩轰簬CPU ID鐢熸垚鍞竴绉嶅瓙
    ULONG baseSeed1 = 0x12345678 ^ cpuId;
    ULONG baseSeed2 = 0x87654321 ^ (cpuId >> 8);
    ULONG baseSeed3 = 0xABCDEF00 ^ (cpuId >> 16);
    ULONG baseSeed4 = 0x00FEDCBA ^ (cpuId >> 24);

    // 浣跨敤鍥哄畾瀛楃涓插拰CPU ID娣峰悎鐢熸垚
    const char* seed1 = "ReadPhysDevice2024";
    const char* seed2 = "DMAController";
    const char* seed3 = "PhysicalMemory";
    const char* seed4 = "KernelDriver";

    *part1 = SimpleHash(seed1, baseSeed1);
    *part2 = SimpleHash(seed2, baseSeed2);
    *part3 = SimpleHash(seed3, baseSeed3);
    *part4 = SimpleHash(seed4, baseSeed4);
}

// 閻㈢喐鍨氶梾蹇旀簚GUID閺嶇厧绱￠惃鍕啎婢跺洤鎮曠粔锟�
NTSTATUS GenerateRandomDeviceName(PUNICODE_STRING DeviceName, PUNICODE_STRING DosDeviceName)
{
    NTSTATUS status;
    PWCHAR deviceBuffer = NULL;
    PWCHAR dosBuffer = NULL;
    PWCHAR formatBuffer = NULL;
    
    // 閸掑棝鍘ょ拋鎯ь槵閸氬秶袨缂傛挸鍟块崠锟�
    deviceBuffer = (PWCHAR)KLI_CACHED_CALL(ExAllocatePool2, POOL_FLAG_NON_PAGED, 256, 'maDN');
    if (!deviceBuffer) {
        return STATUS_INSUFFICIENT_RESOURCES;
    }
    
    dosBuffer = (PWCHAR)KLI_CACHED_CALL(ExAllocatePool2, POOL_FLAG_NON_PAGED, 256, 'maDN');
    if (!dosBuffer) {
        KLI_CACHED_CALL(ExFreePool, deviceBuffer);
        return STATUS_INSUFFICIENT_RESOURCES;
    }
    
    formatBuffer = (PWCHAR)KLI_CACHED_CALL(ExAllocatePool2, POOL_FLAG_NON_PAGED, 128, 'tmtF');
    if (!formatBuffer) {
        KLI_CACHED_CALL(ExFreePool, deviceBuffer);
        KLI_CACHED_CALL(ExFreePool, dosBuffer);
        return STATUS_INSUFFICIENT_RESOURCES;
    }
    
    // 閻㈢喐鍨氶崣顖烆暕濞村娈慓UID缂佸嫪娆� - R3缁嬪绨崣顖欎簰娴ｈ法鏁ら惄绋挎倱缁犳纭�
    ULONG part1, part2, part3, part4;
    GeneratePredictableGUID(&part1, &part2, &part3, &part4);
    
    // 鐟欙絾璐╁ǎ鍡氼啎婢跺洦鐗稿蹇撶摟缁楋缚瑕�
    DeobfuscateString(formatBuffer, g_DeviceFormat, sizeof(g_DeviceFormat)/2, 0xAA);
    
    // 閺嬪嫬缂撶拋鎯ь槵閸氬秶袨 (缁鎶€娴滃骸鎮庡▔鏇犳畱鐠佹儳顦珿UID閺嶇厧绱�)
    status = KLI_CACHED_CALL(RtlStringCchPrintfW, deviceBuffer, 128,
        formatBuffer,
        part1,
        (USHORT)(part2 >> 16),
        (USHORT)(part2 & 0xFFFF),
        (USHORT)(part3 >> 16),
        part4,
        (USHORT)(part3 & 0xFFFF));
    
    if (!NT_SUCCESS(status)) {
        KLI_CACHED_CALL(ExFreePool, deviceBuffer);
        KLI_CACHED_CALL(ExFreePool, dosBuffer);
        KLI_CACHED_CALL(ExFreePool, formatBuffer);
        return status;
    }
    
    // 鐟欙絾璐╁ǎ鍜睴S閺嶇厧绱＄€涙顑佹稉锟�
    DeobfuscateString(formatBuffer, g_DosFormat, sizeof(g_DosFormat)/2, 0xBB);
    
    // 閺嬪嫬缂揇OS鐠佹儳顦崥宥囆�
    status = KLI_CACHED_CALL(RtlStringCchPrintfW, dosBuffer, 128,
        formatBuffer,
        part1,
        (USHORT)(part2 >> 16),
        (USHORT)(part2 & 0xFFFF),
        (USHORT)(part3 >> 16),
        part4,
        (USHORT)(part3 & 0xFFFF));
    
    if (!NT_SUCCESS(status)) {
        KLI_CACHED_CALL(ExFreePool, deviceBuffer);
        KLI_CACHED_CALL(ExFreePool, dosBuffer);
        KLI_CACHED_CALL(ExFreePool, formatBuffer);
        return status;
    }
    
    KLI_CACHED_CALL(RtlInitUnicodeString, DeviceName, deviceBuffer);
    KLI_CACHED_CALL(RtlInitUnicodeString, DosDeviceName, dosBuffer);
    
    // 濞撳懘娴傞獮鍫曞櫞閺€鐐壐瀵繒绱﹂崘鎻掑隘
    memset(formatBuffer, 0, 128);
    KLI_CACHED_CALL(ExFreePool, formatBuffer);
    
    return STATUS_SUCCESS;
}




ULONG ReadVirtualMemory(ULONG64 DirectoryTableBase, PVOID Address, PVOID Buffer, PULONG pSizeRead)
{
	BOOLEAN IsDoneRead;
	ULONG ErrorCode;
	ULONG SizeLeft;
	ULONG OffsetInPage;
	ULONG SizeRead;
	ULONG SizeLeftInPage;
	ULONG Size;
	ULONG64 PhysicalAddress;
	ULONG64 PageSize;
	ULONG64 PageAddress;
	Anti4heatExpert::PHYSICAL_PAGE_INFO TransferPageInfo;

	SizeRead = 0;
	memset(&TransferPageInfo, 0, sizeof(TransferPageInfo));
	if (!DirectoryTableBase)
	{
		DirectoryTableBase = __readcr3();
	}
	if (Address && Buffer && pSizeRead && *pSizeRead)
	{
		if (KLI_CACHED_CALL(KeGetCurrentIrql) <= 2u)
		{
			Anti4heatExpert::AllocatePhysicalPage(&TransferPageInfo, 0x1000);
			PageSize = 0;
			ErrorCode = Anti4heatExpert::GetPhysPageSize(&TransferPageInfo, (ULONG64)Address, &PageSize, DirectoryTableBase);
			if (!ErrorCode && PageSize > 0x1000)
			{
				ErrorCode = 1;
				goto _EXIT;
			}
			OffsetInPage = (ULONG64)Address & 0xFFF;
			PageAddress = (ULONG64)Address & 0xFFFFFFFFFFFFF000u;
			Size = *pSizeRead;
			SizeLeft = *pSizeRead;
			do
			{
				IsDoneRead = FALSE;
				if (SizeLeft >= PAGE_SIZE - OffsetInPage)
					SizeLeftInPage = PAGE_SIZE - OffsetInPage;
				else
					SizeLeftInPage = SizeLeft;

				PhysicalAddress = 0;
				ErrorCode = Anti4heatExpert::GetPhysPageAddress(&TransferPageInfo, DirectoryTableBase, (PVOID)PageAddress, &PhysicalAddress);

				if (!ErrorCode && PhysicalAddress
					&& !Anti4heatExpert::ReadPhysicalPage(
						&TransferPageInfo,
						PhysicalAddress + OffsetInPage,
						Buffer,
						SizeLeftInPage))
				{
					SizeRead += SizeLeftInPage;
					IsDoneRead = TRUE;
				}
				if (!IsDoneRead)
					memset(Buffer, 0, SizeLeftInPage);
				Buffer = (PUCHAR)Buffer + SizeLeftInPage;
				PageAddress += OffsetInPage + (ULONG64)SizeLeftInPage;
				OffsetInPage = 0;
				SizeLeft -= SizeLeftInPage;
			} while (SizeLeft && SizeLeft < Size);
			if (SizeRead)
			{
				*pSizeRead = SizeRead;
				ErrorCode = 0;
			}
			else
			{
				ErrorCode = 216;
			}
		}
		else
		{
			ErrorCode = 261;
		}
	}
	else
	{
		ErrorCode = 22;
	}
	_EXIT:
	Anti4heatExpert::FreePhysicalPage(&TransferPageInfo);
	return ErrorCode;
}

// 鐩撮敓鎺ヨ鎷峰彇閿熸枻鎷烽敓鏂ゆ嫹閿熻妭杈炬嫹閿熻鍑ゆ嫹閿熸枻鎷烽敓锟�
ULONG ReadPhysicalMemory(ULONG64 PhysicalAddress, PVOID Buffer, ULONG Size, PULONG pSizeRead)
{
	ULONG ErrorCode;
	ULONG SizeRead = 0;
	ULONG SizeLeft = Size;
	ULONG SizeLeftInPage;
	ULONG64 PhysPageBase;
	ULONG OffsetInPage;
	Anti4heatExpert::PHYSICAL_PAGE_INFO TransferPageInfo;

	memset(&TransferPageInfo, 0, sizeof(TransferPageInfo));
	
	if (!Buffer || !pSizeRead || !Size)
	{
		return 22; // ERROR_INVALID_PARAMETER
	}

	*pSizeRead = 0;

	if (KLI_CACHED_CALL(KeGetCurrentIrql) > 2u)
	{
		return 261; // ERROR_INVALID_LEVEL
	}

	// 閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹椤甸敓鏂ゆ嫹閿熻妭杈炬嫹閿熸枻鎷�
	ErrorCode = Anti4heatExpert::AllocatePhysicalPage(&TransferPageInfo, 0x1000);
	if (ErrorCode)
	{
		return ErrorCode;
	}

	while (SizeLeft > 0)
	{
		PhysPageBase = PhysicalAddress & 0xFFFFFFFFFFFFF000ULL; // 椤甸敓鏂ゆ嫹閿熸枻鎷�
		OffsetInPage = (ULONG)(PhysicalAddress & 0xFFF);
		
		// 閿熸枻鎷烽敓濮愭湰閿熻娇璁规嫹鍙栭敓鏂ゆ嫹灏�
		if (SizeLeft >= PAGE_SIZE - OffsetInPage)
			SizeLeftInPage = PAGE_SIZE - OffsetInPage;
		else
			SizeLeftInPage = SizeLeft;

		// 閿熸枻鎷峰彇閿熸枻鎷烽敓鏂ゆ嫹椤�
		ErrorCode = Anti4heatExpert::ReadPhysicalPage(
			&TransferPageInfo,
			PhysPageBase + OffsetInPage,
			(PUCHAR)Buffer + SizeRead,
			SizeLeftInPage);

		if (ErrorCode)
		{
			// 閿熸枻鎷峰彇澶遍敓鏉帮綇鎷烽敓鏂ゆ嫹閿燂拷0
			memset((PUCHAR)Buffer + SizeRead, 0, SizeLeftInPage);
		}
		else
		{
			SizeRead += SizeLeftInPage;
		}

		PhysicalAddress += SizeLeftInPage;
		SizeLeft -= SizeLeftInPage;
	}

	Anti4heatExpert::FreePhysicalPage(&TransferPageInfo);
	
	*pSizeRead = SizeRead;
	return SizeRead > 0 ? 0 : 216; // ERROR_ARITHMETIC_OVERFLOW if no data read
}

// IOCTL閿熻鍑ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹
NTSTATUS DmaDeviceControl(PDEVICE_OBJECT DeviceObject, PIRP Irp)
{
	UNREFERENCED_PARAMETER(DeviceObject);
	
	PIO_STACK_LOCATION irpStack = IoGetCurrentIrpStackLocation(Irp);
	NTSTATUS status = STATUS_INVALID_DEVICE_REQUEST;
	ULONG bytesReturned = 0;
	
	switch (irpStack->Parameters.DeviceIoControl.IoControlCode)
	{
		case IOCTL_DMA_READ_MEMORY:
		{
			PDMA_REQUEST request = (PDMA_REQUEST)Irp->AssociatedIrp.SystemBuffer;
			PVOID outputBuffer = Irp->AssociatedIrp.SystemBuffer;
			ULONG inputLength = irpStack->Parameters.DeviceIoControl.InputBufferLength;
			ULONG outputLength = irpStack->Parameters.DeviceIoControl.OutputBufferLength;
			
			if (inputLength >= sizeof(DMA_REQUEST) && 
				outputLength >= request->Length &&
				request->Command == DMA_CMD_READ)
			{
				ULONG bytesRead = 0;
				ULONG errorCode = ReadPhysicalMemory(
					request->Address,
					outputBuffer,
					request->Length,
					&bytesRead);
					
				if (errorCode == 0)
				{
					bytesReturned = bytesRead;
					status = STATUS_SUCCESS;
				}
				else
				{
					status = STATUS_UNSUCCESSFUL;
				}
			}
			else
			{
				status = STATUS_INVALID_PARAMETER;
			}
			break;
		}
		
		case IOCTL_DMA_WRITE_MEMORY:
		{
			// 閺嗗倹妞傛稉宥呯杽閻滄澘鍟撻崗銉ュ閼筹拷
			status = STATUS_NOT_IMPLEMENTED;
			break;
		}
		
		case IOCTL_DMA_IDENTIFY:
		{
			// 鐠佹儳顦拠鍡楀焼鐠囬攱鐪�
			ULONG outputLength = irpStack->Parameters.DeviceIoControl.OutputBufferLength;
			if (outputLength >= sizeof(DWORD)) {
				*(DWORD*)Irp->AssociatedIrp.SystemBuffer = DMA_DEVICE_SIGNATURE;
				bytesReturned = sizeof(DWORD);
				status = STATUS_SUCCESS;
			} else {
				status = STATUS_INVALID_PARAMETER;
			}
			break;
		}
		
		default:
			status = STATUS_INVALID_DEVICE_REQUEST;
			break;
	}
	
	Irp->IoStatus.Status = status;
	Irp->IoStatus.Information = bytesReturned;
	KLI_CACHED_CALL(IofCompleteRequest, Irp, IO_NO_INCREMENT);
	
	return status;
}

// 閫氶敓鐭垎鍑ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹
NTSTATUS DmaDispatchCreateClose(PDEVICE_OBJECT DeviceObject, PIRP Irp)
{
	UNREFERENCED_PARAMETER(DeviceObject);
	
	Irp->IoStatus.Status = STATUS_SUCCESS;
	Irp->IoStatus.Information = 0;
	KLI_CACHED_CALL(IofCompleteRequest,Irp, IO_NO_INCREMENT);
	
	return STATUS_SUCCESS;
}


EXTERN_C NTSTATUS DriverEntry(PDRIVER_OBJECT DriverObject, PUNICODE_STRING RegistryPath)
{
	UNREFERENCED_PARAMETER(RegistryPath);

	NTSTATUS status;
	PDEVICE_OBJECT deviceObject = NULL;

	// 閸掓繂顫愰崠鏉抣i缂傛挸鐡ㄩ惃鍕毐閺佺増瀵氶柦锟�
	KLI_CACHED_SET(KeGetCurrentIrql);
	KLI_CACHED_SET(DbgPrint);
	KLI_CACHED_SET(ExAllocatePool2);
	KLI_CACHED_SET(ExFreePool);
	KLI_CACHED_SET(IoCreateDevice);
	KLI_CACHED_SET(IoCreateSymbolicLink);
	KLI_CACHED_SET(IoDeleteDevice);
	KLI_CACHED_SET(IoDeleteSymbolicLink);
	KLI_CACHED_SET(IofCompleteRequest);
	KLI_CACHED_SET(RtlInitUnicodeString);
	KLI_CACHED_SET(RtlStringCchPrintfW);
	KLI_CACHED_SET(KeQueryTimeIncrement);
	
	// 閸掓繂顫愰崠鏈卬ti4heatExpert閻ㄥ埍li缂傛挸鐡�
	Anti4heatExpert::InitializeKliCache();

	// 閻㈢喐鍨氶梾蹇旀簚GUID閺嶇厧绱￠惃鍕啎婢跺洤鎮曠粔锟�
	status = GenerateRandomDeviceName(&g_DeviceName, &g_DosDeviceName);
	if (!NT_SUCCESS(status)) {
		KLI_CACHED_CALL(DbgPrint, "[!] Failed to generate random device name: 0x%08X\n", status);
		return status;
	}

	KLI_CACHED_CALL(DbgPrint, "[+] Generated device name: %wZ\n", &g_DeviceName);

	// 閸掓稑缂撶拋鎯ь槵鐎电钖�
	status = KLI_CACHED_CALL(IoCreateDevice,
		DriverObject,
		0,
		&g_DeviceName,
		FILE_DEVICE_UNKNOWN,
		FILE_DEVICE_SECURE_OPEN,
		FALSE,
		&deviceObject);

	if (!NT_SUCCESS(status))
	{
		KLI_CACHED_CALL(DbgPrint, "[!] Failed to create device object: 0x%08X\n", status);
		// 濞撳懐鎮婇崚鍡涘帳閻ㄥ嫮绱﹂崘鎻掑隘
		if (g_DeviceName.Buffer) KLI_CACHED_CALL(ExFreePool, g_DeviceName.Buffer);
		if (g_DosDeviceName.Buffer) KLI_CACHED_CALL(ExFreePool, g_DosDeviceName.Buffer);
		return status;
	}

	// 閸掓稑缂撶粭锕€褰块柧鐐复
	status = KLI_CACHED_CALL(IoCreateSymbolicLink, &g_DosDeviceName, &g_DeviceName);
	if (!NT_SUCCESS(status))
	{
		KLI_CACHED_CALL(DbgPrint, "[!] Failed to create symbolic link: 0x%08X\n", status);
		KLI_CACHED_CALL(IoDeleteDevice, deviceObject);
		// 濞撳懐鎮婇崚鍡涘帳閻ㄥ嫮绱﹂崘鎻掑隘
		if (g_DeviceName.Buffer) KLI_CACHED_CALL(ExFreePool, g_DeviceName.Buffer);
		if (g_DosDeviceName.Buffer) KLI_CACHED_CALL(ExFreePool, g_DosDeviceName.Buffer);
		return status;
	}

	// 閿熸枻鎷烽敓鐭垎鍑ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹
	DriverObject->MajorFunction[IRP_MJ_CREATE] = DmaDispatchCreateClose;
	DriverObject->MajorFunction[IRP_MJ_CLOSE] = DmaDispatchCreateClose;
	DriverObject->MajorFunction[IRP_MJ_DEVICE_CONTROL] = DmaDeviceControl;

	DriverObject->DriverUnload = [](PDRIVER_OBJECT DriverObject)->VOID {
		// 閸掔娀娅庣粭锕€褰块柧鐐复閸滃矁顔曟径鍥ь嚠鐠烇拷
		if (g_DosDeviceName.Buffer) {
			KLI_CACHED_CALL(IoDeleteSymbolicLink, &g_DosDeviceName);
			KLI_CACHED_CALL(ExFreePool, g_DosDeviceName.Buffer);
		}
		
		if (DriverObject->DeviceObject)
		{
			KLI_CACHED_CALL(IoDeleteDevice, DriverObject->DeviceObject);
		}
		
		// 濞撳懐鎮婄拋鎯ь槵閸氬秶袨缂傛挸鍟块崠锟�
		if (g_DeviceName.Buffer) {
			KLI_CACHED_CALL(ExFreePool, g_DeviceName.Buffer);
		}
		
		KLI_CACHED_CALL(DbgPrint, "[+] DMA Driver unloaded\n");
	};

	// 閿熸枻鎷烽敓鏂ゆ嫹閿熷€熷閿熸枻鎷峰織
	deviceObject->Flags |= DO_BUFFERED_IO;
	deviceObject->Flags &= ~DO_DEVICE_INITIALIZING;

	KLI_CACHED_CALL(DbgPrint, "[+] DMA Driver loaded successfully\n");
	
	return STATUS_SUCCESS;
}

	};

	// 閿熸枻鎷烽敓鏂ゆ嫹閿熷€熷閿熸枻鎷峰織
	deviceObject->Flags |= DO_BUFFERED_IO;
	deviceObject->Flags &= ~DO_DEVICE_INITIALIZING;

	KLI_CACHED_CALL(DbgPrint, "[+] DMA Driver loaded successfully\n");
	
	return STATUS_SUCCESS;
}

	};

	// 锟斤拷锟斤拷锟借备锟斤拷志
	deviceObject->Flags |= DO_BUFFERED_IO;
	deviceObject->Flags &= ~DO_DEVICE_INITIALIZING;

	KLI_CACHED_CALL(DbgPrint, "[+] DMA Driver loaded successfully\n");
	
	return STATUS_SUCCESS;
}

