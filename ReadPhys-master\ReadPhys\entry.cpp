﻿#include <ntifs.h>
#include "Anti4heatExpert.h"
#include "kli.hpp"
#include <ntstrsafe.h>

// 内核驱动DMA协议定义 - 避免包含用户模式头文件
#define DMA_MAGIC 0x444D4131  // "DMA1"
#define DMA_CMD_READ  0x01
#define DMA_CMD_WRITE 0x02

// 驱动IOCTL定义
#define IOCTL_DMA_READ_MEMORY   CTL_CODE(FILE_DEVICE_UNKNOWN, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_DMA_WRITE_MEMORY  CTL_CODE(FILE_DEVICE_UNKNOWN, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_DMA_IDENTIFY      CTL_CODE(FILE_DEVICE_UNKNOWN, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS)

// 设备识别标识符
#define DMA_DEVICE_SIGNATURE    0x444D4144  // "DMAD"

// 驱动请求结构
typedef struct _DMA_REQUEST {
    DWORD Command;      // DMA_CMD_READ 或 DMA_CMD_WRITE
    ULONG64 Address;    // 物理内存地址
    DWORD Length;       // 数据长度
    DWORD Reserved;     // 保留字段
} DMA_REQUEST, *PDMA_REQUEST;

// 定义缓存的kli函数指针
KLI_CACHED_DEF(KeGetCurrentIrql);
KLI_CACHED_DEF(DbgPrint);
KLI_CACHED_DEF(ExAllocatePool2);
KLI_CACHED_DEF(ExFreePool);
KLI_CACHED_DEF(IoCreateDevice);
KLI_CACHED_DEF(IoCreateSymbolicLink);
KLI_CACHED_DEF(IoDeleteDevice);
KLI_CACHED_DEF(IoDeleteSymbolicLink);
KLI_CACHED_DEF(IofCompleteRequest);
KLI_CACHED_DEF(RtlInitUnicodeString);
KLI_CACHED_DEF(RtlStringCchPrintfW);
KLI_CACHED_DEF(KeQueryTimeIncrement);

// 全局变量保存设备名称
UNICODE_STRING g_DeviceName;
UNICODE_STRING g_DosDeviceName;

// 简单的字符串解混淆函数
VOID DeobfuscateString(PWCHAR dest, const UCHAR* src, ULONG len, UCHAR key)
{
    for (ULONG i = 0; i < len; i++) {
        dest[i] = (WCHAR)(src[i] ^ key);
    }
    dest[len] = L'\0';
}

// 混淆的字符串数据
static const UCHAR g_DeviceFormat[] = {
    0x6F^0xAA, 0x00^0xAA, 0x44^0xAA, 0x00^0xAA, 0x65^0xAA, 0x00^0xAA, 0x76^0xAA, 0x00^0xAA, 0x69^0xAA, 0x00^0xAA, 0x63^0xAA, 0x00^0xAA, 0x65^0xAA, 0x00^0xAA, 0x6F^0xAA, 0x00^0xAA,
    0x6F^0xAA, 0x00^0xAA, 0x7B^0xAA, 0x00^0xAA, 0x25^0xAA, 0x00^0xAA, 0x30^0xAA, 0x00^0xAA, 0x38^0xAA, 0x00^0xAA, 0x78^0xAA, 0x00^0xAA, 0x2D^0xAA, 0x00^0xAA, 0x25^0xAA, 0x00^0xAA,
    0x30^0xAA, 0x00^0xAA, 0x34^0xAA, 0x00^0xAA, 0x78^0xAA, 0x00^0xAA, 0x2D^0xAA, 0x00^0xAA, 0x25^0xAA, 0x00^0xAA, 0x30^0xAA, 0x00^0xAA, 0x34^0xAA, 0x00^0xAA, 0x78^0xAA, 0x00^0xAA,
    0x2D^0xAA, 0x00^0xAA, 0x25^0xAA, 0x00^0xAA, 0x30^0xAA, 0x00^0xAA, 0x34^0xAA, 0x00^0xAA, 0x78^0xAA, 0x00^0xAA, 0x2D^0xAA, 0x00^0xAA, 0x25^0xAA, 0x00^0xAA, 0x30^0xAA, 0x00^0xAA,
    0x38^0xAA, 0x00^0xAA, 0x78^0xAA, 0x00^0xAA, 0x25^0xAA, 0x00^0xAA, 0x30^0xAA, 0x00^0xAA, 0x34^0xAA, 0x00^0xAA, 0x78^0xAA, 0x00^0xAA, 0x7D^0xAA, 0x00^0xAA
};

static const UCHAR g_DosFormat[] = {
    0x6F^0xBB, 0x00^0xBB, 0x44^0xBB, 0x00^0xBB, 0x6F^0xBB, 0x00^0xBB, 0x73^0xBB, 0x00^0xBB, 0x44^0xBB, 0x00^0xBB, 0x65^0xBB, 0x00^0xBB, 0x76^0xBB, 0x00^0xBB, 0x69^0xBB, 0x00^0xBB,
    0x63^0xBB, 0x00^0xBB, 0x65^0xBB, 0x00^0xBB, 0x73^0xBB, 0x00^0xBB, 0x6F^0xBB, 0x00^0xBB, 0x7B^0xBB, 0x00^0xBB, 0x25^0xBB, 0x00^0xBB, 0x30^0xBB, 0x00^0xBB, 0x38^0xBB, 0x00^0xBB,
    0x78^0xBB, 0x00^0xBB, 0x2D^0xBB, 0x00^0xBB, 0x25^0xBB, 0x00^0xBB, 0x30^0xBB, 0x00^0xBB, 0x34^0xBB, 0x00^0xBB, 0x78^0xBB, 0x00^0xBB, 0x2D^0xBB, 0x00^0xBB, 0x25^0xBB, 0x00^0xBB,
    0x30^0xBB, 0x00^0xBB, 0x34^0xBB, 0x00^0xBB, 0x78^0xBB, 0x00^0xBB, 0x2D^0xBB, 0x00^0xBB, 0x25^0xBB, 0x00^0xBB, 0x30^0xBB, 0x00^0xBB, 0x34^0xBB, 0x00^0xBB, 0x78^0xBB, 0x00^0xBB,
    0x2D^0xBB, 0x00^0xBB, 0x25^0xBB, 0x00^0xBB, 0x30^0xBB, 0x00^0xBB, 0x38^0xBB, 0x00^0xBB, 0x78^0xBB, 0x00^0xBB, 0x25^0xBB, 0x00^0xBB, 0x30^0xBB, 0x00^0xBB, 0x34^0xBB, 0x00^0xBB,
    0x78^0xBB, 0x00^0xBB, 0x7D^0xBB, 0x00^0xBB
};

// 简单的随机数生成器
ULONG g_RandomSeed = 0;

ULONG SimpleRandom()
{
    g_RandomSeed = (g_RandomSeed * 1103515245 + 12345) & 0x7FFFFFFF;
    return g_RandomSeed;
}

// 生成随机GUID格式的设备名称
NTSTATUS GenerateRandomDeviceName(PUNICODE_STRING DeviceName, PUNICODE_STRING DosDeviceName)
{
    NTSTATUS status;
    PWCHAR deviceBuffer = NULL;
    PWCHAR dosBuffer = NULL;
    PWCHAR formatBuffer = NULL;
    
    // 分配设备名称缓冲区
    deviceBuffer = (PWCHAR)KLI_CACHED_CALL(ExAllocatePool2, POOL_FLAG_NON_PAGED, 256, 'maDN');
    if (!deviceBuffer) {
        return STATUS_INSUFFICIENT_RESOURCES;
    }
    
    dosBuffer = (PWCHAR)KLI_CACHED_CALL(ExAllocatePool2, POOL_FLAG_NON_PAGED, 256, 'maDN');
    if (!dosBuffer) {
        KLI_CACHED_CALL(ExFreePool, deviceBuffer);
        return STATUS_INSUFFICIENT_RESOURCES;
    }
    
    formatBuffer = (PWCHAR)KLI_CACHED_CALL(ExAllocatePool2, POOL_FLAG_NON_PAGED, 128, 'tmtF');
    if (!formatBuffer) {
        KLI_CACHED_CALL(ExFreePool, deviceBuffer);
        KLI_CACHED_CALL(ExFreePool, dosBuffer);
        return STATUS_INSUFFICIENT_RESOURCES;
    }
    
    // 使用时间作为种子
    if (g_RandomSeed == 0) {
        LARGE_INTEGER time;
        KeQuerySystemTime(&time);
        g_RandomSeed = (ULONG)(time.LowPart ^ time.HighPart);
    }
    
    // 生成伪GUID格式: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
    ULONG part1 = SimpleRandom();
    ULONG part2 = SimpleRandom();
    ULONG part3 = SimpleRandom();
    ULONG part4 = SimpleRandom();
    
    // 解混淆设备格式字符串
    DeobfuscateString(formatBuffer, g_DeviceFormat, sizeof(g_DeviceFormat)/2, 0xAA);
    
    // 构建设备名称 (类似于合法的设备GUID格式)
    status = KLI_CACHED_CALL(RtlStringCchPrintfW, deviceBuffer, 128,
        formatBuffer,
        part1,
        (USHORT)(part2 >> 16),
        (USHORT)(part2 & 0xFFFF),
        (USHORT)(part3 >> 16),
        part4,
        (USHORT)(part3 & 0xFFFF));
    
    if (!NT_SUCCESS(status)) {
        KLI_CACHED_CALL(ExFreePool, deviceBuffer);
        KLI_CACHED_CALL(ExFreePool, dosBuffer);
        KLI_CACHED_CALL(ExFreePool, formatBuffer);
        return status;
    }
    
    // 解混淆DOS格式字符串
    DeobfuscateString(formatBuffer, g_DosFormat, sizeof(g_DosFormat)/2, 0xBB);
    
    // 构建DOS设备名称
    status = KLI_CACHED_CALL(RtlStringCchPrintfW, dosBuffer, 128,
        formatBuffer,
        part1,
        (USHORT)(part2 >> 16),
        (USHORT)(part2 & 0xFFFF),
        (USHORT)(part3 >> 16),
        part4,
        (USHORT)(part3 & 0xFFFF));
    
    if (!NT_SUCCESS(status)) {
        KLI_CACHED_CALL(ExFreePool, deviceBuffer);
        KLI_CACHED_CALL(ExFreePool, dosBuffer);
        KLI_CACHED_CALL(ExFreePool, formatBuffer);
        return status;
    }
    
    KLI_CACHED_CALL(RtlInitUnicodeString, DeviceName, deviceBuffer);
    KLI_CACHED_CALL(RtlInitUnicodeString, DosDeviceName, dosBuffer);
    
    // 清零并释放格式缓冲区
    memset(formatBuffer, 0, 128);
    KLI_CACHED_CALL(ExFreePool, formatBuffer);
    
    return STATUS_SUCCESS;
}




ULONG ReadVirtualMemory(ULONG64 DirectoryTableBase, PVOID Address, PVOID Buffer, PULONG pSizeRead)
{
	BOOLEAN IsDoneRead;
	ULONG ErrorCode;
	ULONG SizeLeft;
	ULONG OffsetInPage;
	ULONG SizeRead;
	ULONG SizeLeftInPage;
	ULONG Size;
	ULONG64 PhysicalAddress;
	ULONG64 PageSize;
	ULONG64 PageAddress;
	Anti4heatExpert::PHYSICAL_PAGE_INFO TransferPageInfo;

	SizeRead = 0;
	memset(&TransferPageInfo, 0, sizeof(TransferPageInfo));
	if (!DirectoryTableBase)
	{
		DirectoryTableBase = __readcr3();
	}
	if (Address && Buffer && pSizeRead && *pSizeRead)
	{
		if (KLI_CACHED_CALL(KeGetCurrentIrql) <= 2u)
		{
			Anti4heatExpert::AllocatePhysicalPage(&TransferPageInfo, 0x1000);
			PageSize = 0;
			ErrorCode = Anti4heatExpert::GetPhysPageSize(&TransferPageInfo, (ULONG64)Address, &PageSize, DirectoryTableBase);
			if (!ErrorCode && PageSize > 0x1000)
			{
				ErrorCode = 1;
				goto _EXIT;
			}
			OffsetInPage = (ULONG64)Address & 0xFFF;
			PageAddress = (ULONG64)Address & 0xFFFFFFFFFFFFF000u;
			Size = *pSizeRead;
			SizeLeft = *pSizeRead;
			do
			{
				IsDoneRead = FALSE;
				if (SizeLeft >= PAGE_SIZE - OffsetInPage)
					SizeLeftInPage = PAGE_SIZE - OffsetInPage;
				else
					SizeLeftInPage = SizeLeft;

				PhysicalAddress = 0;
				ErrorCode = Anti4heatExpert::GetPhysPageAddress(&TransferPageInfo, DirectoryTableBase, (PVOID)PageAddress, &PhysicalAddress);

				if (!ErrorCode && PhysicalAddress
					&& !Anti4heatExpert::ReadPhysicalPage(
						&TransferPageInfo,
						PhysicalAddress + OffsetInPage,
						Buffer,
						SizeLeftInPage))
				{
					SizeRead += SizeLeftInPage;
					IsDoneRead = TRUE;
				}
				if (!IsDoneRead)
					memset(Buffer, 0, SizeLeftInPage);
				Buffer = (PUCHAR)Buffer + SizeLeftInPage;
				PageAddress += OffsetInPage + (ULONG64)SizeLeftInPage;
				OffsetInPage = 0;
				SizeLeft -= SizeLeftInPage;
			} while (SizeLeft && SizeLeft < Size);
			if (SizeRead)
			{
				*pSizeRead = SizeRead;
				ErrorCode = 0;
			}
			else
			{
				ErrorCode = 216;
			}
		}
		else
		{
			ErrorCode = 261;
		}
	}
	else
	{
		ErrorCode = 22;
	}
	_EXIT:
	Anti4heatExpert::FreePhysicalPage(&TransferPageInfo);
	return ErrorCode;
}

// ֱ�Ӷ�ȡ�����ڴ��ַ����
ULONG ReadPhysicalMemory(ULONG64 PhysicalAddress, PVOID Buffer, ULONG Size, PULONG pSizeRead)
{
	ULONG ErrorCode;
	ULONG SizeRead = 0;
	ULONG SizeLeft = Size;
	ULONG SizeLeftInPage;
	ULONG64 PhysPageBase;
	ULONG OffsetInPage;
	Anti4heatExpert::PHYSICAL_PAGE_INFO TransferPageInfo;

	memset(&TransferPageInfo, 0, sizeof(TransferPageInfo));
	
	if (!Buffer || !pSizeRead || !Size)
	{
		return 22; // ERROR_INVALID_PARAMETER
	}

	*pSizeRead = 0;

	if (KLI_CACHED_CALL(KeGetCurrentIrql) > 2u)
	{
		return 261; // ERROR_INVALID_LEVEL
	}

	// ��������ҳ���ڴ���
	ErrorCode = Anti4heatExpert::AllocatePhysicalPage(&TransferPageInfo, 0x1000);
	if (ErrorCode)
	{
		return ErrorCode;
	}

	while (SizeLeft > 0)
	{
		PhysPageBase = PhysicalAddress & 0xFFFFFFFFFFFFF000ULL; // ҳ����
		OffsetInPage = (ULONG)(PhysicalAddress & 0xFFF);
		
		// ���㱾�ζ�ȡ��С
		if (SizeLeft >= PAGE_SIZE - OffsetInPage)
			SizeLeftInPage = PAGE_SIZE - OffsetInPage;
		else
			SizeLeftInPage = SizeLeft;

		// ��ȡ����ҳ
		ErrorCode = Anti4heatExpert::ReadPhysicalPage(
			&TransferPageInfo,
			PhysPageBase + OffsetInPage,
			(PUCHAR)Buffer + SizeRead,
			SizeLeftInPage);

		if (ErrorCode)
		{
			// ��ȡʧ�ܣ����0
			memset((PUCHAR)Buffer + SizeRead, 0, SizeLeftInPage);
		}
		else
		{
			SizeRead += SizeLeftInPage;
		}

		PhysicalAddress += SizeLeftInPage;
		SizeLeft -= SizeLeftInPage;
	}

	Anti4heatExpert::FreePhysicalPage(&TransferPageInfo);
	
	*pSizeRead = SizeRead;
	return SizeRead > 0 ? 0 : 216; // ERROR_ARITHMETIC_OVERFLOW if no data read
}

// IOCTL�ַ�����
NTSTATUS DmaDeviceControl(PDEVICE_OBJECT DeviceObject, PIRP Irp)
{
	UNREFERENCED_PARAMETER(DeviceObject);
	
	PIO_STACK_LOCATION irpStack = IoGetCurrentIrpStackLocation(Irp);
	NTSTATUS status = STATUS_INVALID_DEVICE_REQUEST;
	ULONG bytesReturned = 0;
	
	switch (irpStack->Parameters.DeviceIoControl.IoControlCode)
	{
		case IOCTL_DMA_READ_MEMORY:
		{
			PDMA_REQUEST request = (PDMA_REQUEST)Irp->AssociatedIrp.SystemBuffer;
			PVOID outputBuffer = Irp->AssociatedIrp.SystemBuffer;
			ULONG inputLength = irpStack->Parameters.DeviceIoControl.InputBufferLength;
			ULONG outputLength = irpStack->Parameters.DeviceIoControl.OutputBufferLength;
			
			if (inputLength >= sizeof(DMA_REQUEST) && 
				outputLength >= request->Length &&
				request->Command == DMA_CMD_READ)
			{
				ULONG bytesRead = 0;
				ULONG errorCode = ReadPhysicalMemory(
					request->Address,
					outputBuffer,
					request->Length,
					&bytesRead);
					
				if (errorCode == 0)
				{
					bytesReturned = bytesRead;
					status = STATUS_SUCCESS;
				}
				else
				{
					status = STATUS_UNSUCCESSFUL;
				}
			}
			else
			{
				status = STATUS_INVALID_PARAMETER;
			}
			break;
		}
		
		case IOCTL_DMA_WRITE_MEMORY:
		{
			// 暂时不实现写入功能
			status = STATUS_NOT_IMPLEMENTED;
			break;
		}
		
		case IOCTL_DMA_IDENTIFY:
		{
			// 设备识别请求
			ULONG outputLength = irpStack->Parameters.DeviceIoControl.OutputBufferLength;
			if (outputLength >= sizeof(DWORD)) {
				*(DWORD*)Irp->AssociatedIrp.SystemBuffer = DMA_DEVICE_SIGNATURE;
				bytesReturned = sizeof(DWORD);
				status = STATUS_SUCCESS;
			} else {
				status = STATUS_INVALID_PARAMETER;
			}
			break;
		}
		
		default:
			status = STATUS_INVALID_DEVICE_REQUEST;
			break;
	}
	
	Irp->IoStatus.Status = status;
	Irp->IoStatus.Information = bytesReturned;
	KLI_CACHED_CALL(IofCompleteRequest, Irp, IO_NO_INCREMENT);
	
	return status;
}

// ͨ�÷ַ�����
NTSTATUS DmaDispatchCreateClose(PDEVICE_OBJECT DeviceObject, PIRP Irp)
{
	UNREFERENCED_PARAMETER(DeviceObject);
	
	Irp->IoStatus.Status = STATUS_SUCCESS;
	Irp->IoStatus.Information = 0;
	KLI_CACHED_CALL(IofCompleteRequest,Irp, IO_NO_INCREMENT);
	
	return STATUS_SUCCESS;
}


EXTERN_C NTSTATUS DriverEntry(PDRIVER_OBJECT DriverObject, PUNICODE_STRING RegistryPath)
{
	UNREFERENCED_PARAMETER(RegistryPath);

	NTSTATUS status;
	PDEVICE_OBJECT deviceObject = NULL;

	// 初始化kli缓存的函数指针
	KLI_CACHED_SET(KeGetCurrentIrql);
	KLI_CACHED_SET(DbgPrint);
	KLI_CACHED_SET(ExAllocatePool2);
	KLI_CACHED_SET(ExFreePool);
	KLI_CACHED_SET(IoCreateDevice);
	KLI_CACHED_SET(IoCreateSymbolicLink);
	KLI_CACHED_SET(IoDeleteDevice);
	KLI_CACHED_SET(IoDeleteSymbolicLink);
	KLI_CACHED_SET(IofCompleteRequest);
	KLI_CACHED_SET(RtlInitUnicodeString);
	KLI_CACHED_SET(RtlStringCchPrintfW);
	KLI_CACHED_SET(KeQueryTimeIncrement);
	
	// 初始化Anti4heatExpert的kli缓存
	Anti4heatExpert::InitializeKliCache();

	// 生成随机GUID格式的设备名称
	status = GenerateRandomDeviceName(&g_DeviceName, &g_DosDeviceName);
	if (!NT_SUCCESS(status)) {
		KLI_CACHED_CALL(DbgPrint, "[!] Failed to generate random device name: 0x%08X\n", status);
		return status;
	}

	KLI_CACHED_CALL(DbgPrint, "[+] Generated device name: %wZ\n", &g_DeviceName);

	// 创建设备对象
	status = KLI_CACHED_CALL(IoCreateDevice,
		DriverObject,
		0,
		&g_DeviceName,
		FILE_DEVICE_UNKNOWN,
		FILE_DEVICE_SECURE_OPEN,
		FALSE,
		&deviceObject);

	if (!NT_SUCCESS(status))
	{
		KLI_CACHED_CALL(DbgPrint, "[!] Failed to create device object: 0x%08X\n", status);
		// 清理分配的缓冲区
		if (g_DeviceName.Buffer) KLI_CACHED_CALL(ExFreePool, g_DeviceName.Buffer);
		if (g_DosDeviceName.Buffer) KLI_CACHED_CALL(ExFreePool, g_DosDeviceName.Buffer);
		return status;
	}

	// 创建符号链接
	status = KLI_CACHED_CALL(IoCreateSymbolicLink, &g_DosDeviceName, &g_DeviceName);
	if (!NT_SUCCESS(status))
	{
		KLI_CACHED_CALL(DbgPrint, "[!] Failed to create symbolic link: 0x%08X\n", status);
		KLI_CACHED_CALL(IoDeleteDevice, deviceObject);
		// 清理分配的缓冲区
		if (g_DeviceName.Buffer) KLI_CACHED_CALL(ExFreePool, g_DeviceName.Buffer);
		if (g_DosDeviceName.Buffer) KLI_CACHED_CALL(ExFreePool, g_DosDeviceName.Buffer);
		return status;
	}

	// ���÷ַ�����
	DriverObject->MajorFunction[IRP_MJ_CREATE] = DmaDispatchCreateClose;
	DriverObject->MajorFunction[IRP_MJ_CLOSE] = DmaDispatchCreateClose;
	DriverObject->MajorFunction[IRP_MJ_DEVICE_CONTROL] = DmaDeviceControl;

	DriverObject->DriverUnload = [](PDRIVER_OBJECT DriverObject)->VOID {
		// 删除符号链接和设备对象
		if (g_DosDeviceName.Buffer) {
			KLI_CACHED_CALL(IoDeleteSymbolicLink, &g_DosDeviceName);
			KLI_CACHED_CALL(ExFreePool, g_DosDeviceName.Buffer);
		}
		
		if (DriverObject->DeviceObject)
		{
			KLI_CACHED_CALL(IoDeleteDevice, DriverObject->DeviceObject);
		}
		
		// 清理设备名称缓冲区
		if (g_DeviceName.Buffer) {
			KLI_CACHED_CALL(ExFreePool, g_DeviceName.Buffer);
		}
		
		KLI_CACHED_CALL(DbgPrint, "[+] DMA Driver unloaded\n");
	};

	// �����豸��־
	deviceObject->Flags |= DO_BUFFERED_IO;
	deviceObject->Flags &= ~DO_DEVICE_INITIALIZING;

	KLI_CACHED_CALL(DbgPrint, "[+] DMA Driver loaded successfully\n");
	
	return STATUS_SUCCESS;
}

