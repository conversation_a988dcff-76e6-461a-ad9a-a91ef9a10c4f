﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{B5BFC522-938A-4A0B-9DEA-2F5E3F4BBE83}</ProjectGuid>
    <TemplateGuid>{dd38f7fc-d7bd-488b-9242-7d8754cde80d}</TemplateGuid>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <MinimumVisualStudioVersion>12.0</MinimumVisualStudioVersion>
    <Configuration>Debug</Configuration>
    <Platform Condition="'$(Platform)' == ''">x64</Platform>
    <RootNamespace>ReadPhys</RootNamespace>
    <WindowsTargetPlatformVersion>$(LatestTargetPlatformVersion)</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <TargetVersion>Windows10</TargetVersion>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>WindowsKernelModeDriver10.0</PlatformToolset>
    <ConfigurationType>Driver</ConfigurationType>
    <DriverType>WDM</DriverType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <TargetVersion>Windows10</TargetVersion>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>OLLVM-MSVC_KernelMode</PlatformToolset>
    <ConfigurationType>Driver</ConfigurationType>
    <DriverType>WDM</DriverType>
    <Driver_SpectreMitigation>false</Driver_SpectreMitigation>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <DebuggerFlavor>DbgengKernelDebugger</DebuggerFlavor>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <DebuggerFlavor>DbgengKernelDebugger</DebuggerFlavor>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <DriverSign>
      <FileDigestAlgorithm>sha256</FileDigestAlgorithm>
    </DriverSign>
    <ClCompile>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <DriverSign>
      <FileDigestAlgorithm>sha256</FileDigestAlgorithm>
    </DriverSign>
    <ClCompile>
      <TreatWarningAsError>false</TreatWarningAsError>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\请勿下载到游戏机\副机\newleechcore\netversion;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>-mllvm -string-obfus -mllvm -const-obfus -mllvm -fla /Gs65536 /GS- /Oy /Oi %(AdditionalOptions)</AdditionalOptions>
      <Optimization>MinSpace</Optimization>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <OmitFramePointers>true</OmitFramePointers>
      <StackProtection>false</StackProtection>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <ExceptionHandling>false</ExceptionHandling>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <SmallerTypeCheck>false</SmallerTypeCheck>
    </ClCompile>
    <Link>
      <AdditionalOptions>/MERGE:.rdata=.text /MERGE:.pdata=.text /MERGE:PAGE=.text /DISCARD:.pdata /FIXED /DYNAMICBASE:NO /NXCOMPAT:NO /SAFESEH:NO /NODEFAULTLIB:libcmt.lib /NODEFAULTLIB:libvcruntime.lib /FORCE:UNRESOLVED %(AdditionalOptions)</AdditionalOptions>
      <EntryPointSymbol>DriverEntry</EntryPointSymbol>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <SetChecksum>false</SetChecksum>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>false</DataExecutionPrevention>
      <TreatLinkerWarningAsErrors>false</TreatLinkerWarningAsErrors>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <AdditionalDependencies>ntoskrnl.lib;hal.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <FilesToPackage Include="$(TargetPath)" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Anti4heatExpert.h" />
    <ClInclude Include="kli.hpp" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Anti4heatExpert.cpp" />
    <ClCompile Include="entry.cpp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>