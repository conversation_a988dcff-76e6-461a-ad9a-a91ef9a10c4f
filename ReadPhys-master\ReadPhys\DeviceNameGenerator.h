#pragma once
#include <windows.h>
#include <string>

/*
 * 设备名称生成算法 - R3程序使用
 * 
 * 这个算法与内核驱动中使用的算法完全相同，
 * 确保R3程序可以计算出正确的设备名称来访问驱动。
 */

class DeviceNameGenerator 
{
public:
    // 与内核驱动中相同的哈希算法
    static ULONG SimpleHash(const char* str, ULONG seed)
    {
        ULONG hash = seed;
        while (*str) {
            hash = hash * 33 + (ULONG)(*str);
            str++;
        }
        return hash;
    }

    // 生成可预测的GUID组件 - 与内核驱动完全相同
    static void GeneratePredictableGUID(ULONG* part1, ULONG* part2, ULONG* part3, ULONG* part4)
    {
        // 使用与内核驱动相同的种子字符串
        const char* seed1 = "ReadPhysDevice2024";
        const char* seed2 = "DMAController";
        const char* seed3 = "PhysicalMemory";
        const char* seed4 = "KernelDriver";
        
        *part1 = SimpleHash(seed1, 0x12345678);
        *part2 = SimpleHash(seed2, 0x87654321);
        *part3 = SimpleHash(seed3, 0xABCDEF00);
        *part4 = SimpleHash(seed4, 0x00FEDCBA);
    }

    // 生成设备名称 - R3程序调用此函数获取设备名称
    static std::wstring GenerateDeviceName()
    {
        ULONG part1, part2, part3, part4;
        GeneratePredictableGUID(&part1, &part2, &part3, &part4);
        
        wchar_t deviceName[256];
        swprintf_s(deviceName, 256,
            L"\\\\.\\{%08x-%04x-%04x-%04x-%08x%04x}",
            part1,
            (USHORT)(part2 >> 16),
            (USHORT)(part2 & 0xFFFF),
            (USHORT)(part3 >> 16),
            part4,
            (USHORT)(part3 & 0xFFFF)
        );
        
        return std::wstring(deviceName);
    }

    // 生成内核设备名称（用于调试）
    static std::wstring GenerateKernelDeviceName()
    {
        ULONG part1, part2, part3, part4;
        GeneratePredictableGUID(&part1, &part2, &part3, &part4);
        
        wchar_t deviceName[256];
        swprintf_s(deviceName, 256,
            L"\\Device\\{%08x-%04x-%04x-%04x-%08x%04x}",
            part1,
            (USHORT)(part2 >> 16),
            (USHORT)(part2 & 0xFFFF),
            (USHORT)(part3 >> 16),
            part4,
            (USHORT)(part3 & 0xFFFF)
        );
        
        return std::wstring(deviceName);
    }
};

/*
 * 使用示例：
 * 
 * #include "DeviceNameGenerator.h"
 * 
 * int main()
 * {
 *     // 获取设备名称
 *     std::wstring deviceName = DeviceNameGenerator::GenerateDeviceName();
 *     
 *     // 打开设备
 *     HANDLE hDevice = CreateFileW(
 *         deviceName.c_str(),
 *         GENERIC_READ | GENERIC_WRITE,
 *         0,
 *         NULL,
 *         OPEN_EXISTING,
 *         FILE_ATTRIBUTE_NORMAL,
 *         NULL
 *     );
 *     
 *     if (hDevice != INVALID_HANDLE_VALUE) {
 *         // 成功打开设备，可以进行IOCTL操作
 *         // ...
 *         CloseHandle(hDevice);
 *     }
 *     
 *     return 0;
 * }
 */
