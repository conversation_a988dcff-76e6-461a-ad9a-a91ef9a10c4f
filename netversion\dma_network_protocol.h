#pragma once
#include <windows.h>
#include <windnsdef.h>

// 网络DMA协议定义
#define DMA_MAGIC 0x444D4131  // "DMA1"
#define DMA_CMD_READ  0x01
#define DMA_CMD_WRITE 0x02

// 协议头
typedef struct _DMA_PACKET_HEADER {
    DWORD magic;        // 协议标识
    DWORD command;      // 命令类型
    QWORD address;      // 目标内存地址
    DWORD length;       // 数据长度
    DWORD reserved;     // 保留字段
} DMA_PACKET_HEADER, *PDMA_PACKET_HEADER;

// 响应包
typedef struct _DMA_RESPONSE {
    DWORD magic;        // 协议标识
    DWORD status;       // 操作状态 (0=成功)
    DWORD length;       // 返回数据长度
    DWORD reserved;     // 保留字段
} DMA_RESPONSE, *PDMA_RESPONSE;

// 驱动IOCTL定义
#define IOCTL_DMA_READ_MEMORY   CTL_CODE(FILE_DEVICE_UNKNOWN, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_DMA_WRITE_MEMORY  CTL_CODE(FILE_DEVICE_UNKNOWN, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_DMA_IDENTIFY      CTL_CODE(FILE_DEVICE_UNKNOWN, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS)

// 设备识别标识符
#define DMA_DEVICE_SIGNATURE    0x444D4144  // "DMAD"

// 驱动请求结构
typedef struct _DMA_REQUEST {
    DWORD Command;      // DMA_CMD_READ 或 DMA_CMD_WRITE
    QWORD Address;      // 物理内存地址
    DWORD Length;       // 数据长度
    DWORD Reserved;     // 保留字段
} DMA_REQUEST, *PDMA_REQUEST;

// 网络设置
#define DMA_SERVER_PORT 8888
#define DMA_MAX_TRANSFER_SIZE (1024 * 1024)  // 1MB最大传输

// R3设备发现函数
BOOL DmaDevice_FindRandomDevice(LPWSTR devicePath, DWORD pathSize);
BOOL DmaDevice_FindRandomDeviceImproved(LPWSTR devicePath, DWORD pathSize);
HANDLE DmaDevice_OpenByPath(LPCWSTR devicePath);

// 客户端接口
BOOL DmaNetwork_Initialize(const char* serverIP, WORD serverPort);
BOOL DmaNetwork_ReadMemory(QWORD address, PVOID buffer, DWORD length, PDWORD bytesRead);
BOOL DmaNetwork_WriteMemory(QWORD address, PVOID buffer, DWORD length, PDWORD bytesWritten);
VOID DmaNetwork_Cleanup();

// 服务端接口
BOOL DmaServer_Initialize(WORD port);
BOOL DmaServer_Start();
VOID DmaServer_Stop();