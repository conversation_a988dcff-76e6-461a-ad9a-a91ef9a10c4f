#include <windows.h>
#include <iostream>
#include "dma_network_protocol.h"

// 外部声明智能发现函数
extern "C" BOOL DmaDevice_FindRandomDeviceImproved(LPWSTR devicePath, DWORD pathSize);

int main()
{
    printf("Improved DMA Device Discovery Test\n");
    printf("==================================\n");

    WCHAR devicePath[MAX_PATH];
    
    // 使用改进的设备发现
    printf("[*] Searching for DMA device using smart discovery...\n");
    
    if (DmaDevice_FindRandomDeviceImproved(devicePath, MAX_PATH)) {
        printf("[+] Found device: %ws\n", devicePath);
        
        // 尝试打开设备
        HANDLE hDevice = DmaDevice_OpenByPath(devicePath);
        if (hDevice != INVALID_HANDLE_VALUE) {
            printf("[+] Successfully opened device handle\n");
            
            // 测试设备通信
            DWORD signature = 0;
            DWORD bytesReturned = 0;
            
            BOOL result = DeviceIoControl(
                hDevice,
                IOCTL_DMA_IDENTIFY,
                NULL, 0,
                &signature, sizeof(signature),
                &bytesReturned,
                NULL
            );
            
            if (result && signature == DMA_DEVICE_SIGNATURE) {
                printf("[+] Device signature verified: 0x%08X\n", signature);
                
                // 测试读取物理内存 (读取第一页)
                printf("[*] Testing physical memory read...\n");
                
                DMA_REQUEST request;
                request.Command = DMA_CMD_READ;
                request.Address = 0x1000;  // 第一页
                request.Length = 0x100;    // 256字节
                request.Reserved = 0;
                
                BYTE buffer[0x100];
                ZeroMemory(buffer, sizeof(buffer));
                
                result = DeviceIoControl(
                    hDevice,
                    IOCTL_DMA_READ_MEMORY,
                    &request, sizeof(request),
                    buffer, sizeof(buffer),
                    &bytesReturned,
                    NULL
                );
                
                if (result) {
                    printf("[+] Successfully read %d bytes from physical address 0x%08llX\n", 
                           bytesReturned, request.Address);
                    
                    // 显示前32字节的十六进制数据
                    printf("[*] First 32 bytes:\n");
                    for (int i = 0; i < 32 && i < (int)bytesReturned; i++) {
                        if (i % 16 == 0) printf("    ");
                        printf("%02X ", buffer[i]);
                        if ((i + 1) % 16 == 0) printf("\n");
                    }
                    if (bytesReturned % 16 != 0) printf("\n");
                    
                    printf("[+] DMA driver is fully functional!\n");
                } else {
                    printf("[!] Physical memory read test failed: %d\n", GetLastError());
                }
            } else {
                printf("[!] Device signature verification failed\n");
            }
            
            CloseHandle(hDevice);
        } else {
            printf("[!] Failed to open device: %d\n", GetLastError());
        }
    } else {
        printf("[!] DMA device not found\n");
        printf("[*] Make sure the ReadPhys driver is loaded\n");
        printf("[*] Use: sc create ReadPhys type= kernel binPath= C:\\path\\to\\ReadPhys.sys\n");
        printf("[*] Then: sc start ReadPhys\n");
    }

    printf("\nPress any key to continue...\n");
    getchar();
    return 0;
}