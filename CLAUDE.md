# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

LeechCore是一个物理内存获取库，用于通过各种硬件和软件方法获取物理内存。它支持多平台（Windows、Linux、macOS），并为PCILeech和MemProcFS提供底层内存访问支持。

## 构建指令

### Windows (Visual Studio)
```bash
# 构建解决方案
msbuild LeechCore.sln /p:Configuration=Release /p:Platform=x64

# 或使用Visual Studio IDE打开LeechCore.sln进行构建
```

### Linux
```bash
# 构建LeechCore库
cd leechcore
make

# 构建LeechAgent for Linux
cd ../leechagent_linux
make

# 构建Python扩展
cd ../leechcorepyc
./pkggen_linux.sh
```

### macOS
```bash
# 构建LeechCore库
cd leechcore
make -f Makefile.macos

# 清理构建产物
make -f Makefile.macos clean
```

## 项目架构

### 核心组件
- **leechcore/**: 核心库实现，包含设备驱动程序和内存访问逻辑
- **leechagent/**: Windows LeechAgent服务，用于远程内存访问
- **leechagent_linux/**: Linux版本的LeechAgent
- **leechcorepyc/**: Python C扩展，提供Python API绑定
- **includes/**: 公共头文件，包含API定义

### 设备驱动架构
LeechCore采用模块化设备驱动架构：
- `device_file.c`: 内存转储文件支持
- `device_fpga.c`: FPGA硬件DMA设备
- `device_vmware.c`: VMware虚拟机内存访问
- `device_pmem.c`: 物理内存设备(/dev/mem)
- `device_hibr.c`: Windows休眠文件支持
- `device_tmd.c`: TotalMeltdown漏洞利用
- `device_usb3380.c`: USB3380设备支持
- `device_vmm.c`: VMM(虚拟机管理器)回环设备

每个设备驱动程序实现统一的接口，通过函数指针在运行时选择。

### RPC和网络通信
- **leechrpc.idl**: RPC接口定义
- **leechrpcclient.c**: RPC客户端实现
- **leechrpcserver.c**: RPC服务器实现（在LeechAgent中）
- **leechrpcshared.c**: 共享RPC代码

LeechAgent支持通过TCP端口28473进行Kerberos认证的安全连接。

### 内存管理和工具
- **ob/**: 对象管理子系统，包含队列、映射、集合等数据结构
- **memmap.c**: 内存映射管理
- **util.c**: 通用工具函数
- **oscompatibility.c**: 跨平台兼容性层

## 常用开发任务

### 添加新设备驱动
1. 在`leechcore/`目录下创建`device_newdevice.c`
2. 实现设备特定的打开、关闭、读写函数
3. 在`leechcore.c`中添加设备初始化调用
4. 更新Makefile以包含新文件

### 调试构建
Linux/macOS可以启用调试标志：
```bash
# 取消注释Makefile中的DEBUG FLAGS部分
export ASAN_OPTIONS=strict_string_checks=1:detect_stack_use_after_return=1
make
```

### Python包构建
```bash
cd leechcorepyc
./pkggen_linux.sh  # 生成Linux Python包
# 然后在pkg_linux目录中运行setup.py
```

## 项目依赖

### Linux依赖
- libusb-1.0: USB设备支持
- gcc编译器工具链
- pkg-config

### Windows依赖  
- Visual Studio 2017或更新版本
- Windows SDK

### 可选依赖
- ms-compress: 内存压缩支持（Linux）
- LeechCore-plugins: 外部设备插件（QEMU、rawtcp等）

## API使用说明

LeechCore提供C/C++、Python和C# API。主要的API入口点在`includes/leechcore.h`中定义。设备通过字符串参数指定，如：
- `"device://pmem"` - 物理内存设备
- `"file://memory.dmp"` - 内存转储文件  
- `"fpga://usb3380"` - USB3380 FPGA设备
- `"leechagent://hostname"` - 远程LeechAgent连接

## 安全注意事项

LeechCore用于内存取证和安全研究。使用时需要：
- 管理员/root权限进行内存访问
- LeechAgent连接默认要求Kerberos认证
- 仅在授权环境中使用硬件DMA设备