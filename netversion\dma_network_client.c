#include "dma_network_protocol.h"
#include <winsock2.h>
#include <ws2tcpip.h>

#pragma comment(lib, "ws2_32.lib")

// 全局客户端状态
static SOCKET g_clientSocket = INVALID_SOCKET;
static struct sockaddr_in g_serverAddr;
static BOOL g_initialized = FALSE;

// 初始化网络客户端
BOOL DmaNetwork_Initialize(const char* serverIP, WORD serverPort) {
    WSADATA wsaData;
    
    if (g_initialized) {
        return TRUE;
    }
    
    // 初始化Winsock
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        return FALSE;
    }
    
    // 创建socket
    g_clientSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (g_clientSocket == INVALID_SOCKET) {
        WSACleanup();
        return FALSE;
    }
    
    // 设置服务器地址
    memset(&g_serverAddr, 0, sizeof(g_serverAddr));
    g_serverAddr.sin_family = AF_INET;
    g_serverAddr.sin_port = htons(serverPort);
    inet_pton(AF_INET, serverIP, &g_serverAddr.sin_addr);
    
    // 连接到服务器
    if (connect(g_clientSocket, (struct sockaddr*)&g_serverAddr, sizeof(g_serverAddr)) == SOCKET_ERROR) {
        closesocket(g_clientSocket);
        g_clientSocket = INVALID_SOCKET;
        WSACleanup();
        return FALSE;
    }
    
    g_initialized = TRUE;
    return TRUE;
}

// 发送数据包
static BOOL SendPacket(PVOID data, DWORD length) {
    DWORD totalSent = 0;
    while (totalSent < length) {
        int sent = send(g_clientSocket, (char*)data + totalSent, length - totalSent, 0);
        if (sent == SOCKET_ERROR) {
            return FALSE;
        }
        totalSent += sent;
    }
    return TRUE;
}

// 接收数据包
static BOOL ReceivePacket(PVOID data, DWORD length) {
    DWORD totalReceived = 0;
    while (totalReceived < length) {
        int received = recv(g_clientSocket, (char*)data + totalReceived, length - totalReceived, 0);
        if (received == SOCKET_ERROR || received == 0) {
            return FALSE;
        }
        totalReceived += received;
    }
    return TRUE;
}

// 读取内存
BOOL DmaNetwork_ReadMemory(QWORD address, PVOID buffer, DWORD length, PDWORD bytesRead) {
    if (!g_initialized || length > DMA_MAX_TRANSFER_SIZE) {
        return FALSE;
    }
    
    // 构建读取请求
    DMA_PACKET_HEADER header;
    header.magic = DMA_MAGIC;
    header.command = DMA_CMD_READ;
    header.address = address;
    header.length = length;
    header.reserved = 0;
    
    // 发送请求
    if (!SendPacket(&header, sizeof(header))) {
        return FALSE;
    }
    
    // 接收响应头
    DMA_RESPONSE response;
    if (!ReceivePacket(&response, sizeof(response))) {
        return FALSE;
    }
    
    // 检查响应
    if (response.magic != DMA_MAGIC || response.status != 0) {
        return FALSE;
    }
    
    // 接收数据
    if (response.length > 0 && response.length <= length) {
        if (!ReceivePacket(buffer, response.length)) {
            return FALSE;
        }
        if (bytesRead) {
            *bytesRead = response.length;
        }
    }
    
    return TRUE;
}

// 写入内存
BOOL DmaNetwork_WriteMemory(QWORD address, PVOID buffer, DWORD length, PDWORD bytesWritten) {
    if (!g_initialized || length > DMA_MAX_TRANSFER_SIZE) {
        return FALSE;
    }
    
    // 构建写入请求
    DMA_PACKET_HEADER header;
    header.magic = DMA_MAGIC;
    header.command = DMA_CMD_WRITE;
    header.address = address;
    header.length = length;
    header.reserved = 0;
    
    // 发送请求头
    if (!SendPacket(&header, sizeof(header))) {
        return FALSE;
    }
    
    // 发送数据
    if (length > 0) {
        if (!SendPacket(buffer, length)) {
            return FALSE;
        }
    }
    
    // 接收响应
    DMA_RESPONSE response;
    if (!ReceivePacket(&response, sizeof(response))) {
        return FALSE;
    }
    
    // 检查响应
    if (response.magic != DMA_MAGIC || response.status != 0) {
        return FALSE;
    }
    
    if (bytesWritten) {
        *bytesWritten = length;
    }
    
    return TRUE;
}

// 清理资源
VOID DmaNetwork_Cleanup() {
    if (g_clientSocket != INVALID_SOCKET) {
        closesocket(g_clientSocket);
        g_clientSocket = INVALID_SOCKET;
    }
    
    if (g_initialized) {
        WSACleanup();
        g_initialized = FALSE;
    }
}

// WinUSB API劫持实现
BOOL NetworkSend(UCHAR PipeID, PUCHAR Buffer, ULONG BufferLength, PULONG LengthTransferred) {
    // 根据PipeID判断操作类型（这里简化处理）
    // 实际实现中需要根据LeechCore的具体协议来解析
    
    // 假设这是写操作，从Buffer解析地址和数据
    if (BufferLength < 16) return FALSE;  // 最小包大小检查
    
    QWORD address = *(QWORD*)Buffer;
    DWORD length = BufferLength - 8;
    
    return DmaNetwork_WriteMemory(address, Buffer + 8, length, LengthTransferred);
}

BOOL NetworkReceive(UCHAR PipeID, PUCHAR Buffer, ULONG BufferLength, PULONG LengthTransferred) {
    // 假设这是读操作，Buffer包含要读取的地址
    if (BufferLength < 8) return FALSE;
    
    QWORD address = *(QWORD*)Buffer;
    DWORD length = BufferLength - 8;
    
    return DmaNetwork_ReadMemory(address, Buffer + 8, length, LengthTransferred);
}