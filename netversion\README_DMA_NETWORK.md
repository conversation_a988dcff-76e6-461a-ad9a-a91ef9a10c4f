# DMA网络替代方案使用说明

## 概述
这个方案通过网络替代损坏的DMA硬件，使用WinUSB API劫持技术将USB操作转换为网络操作。

## 文件说明
- `dma_network_protocol.h` - 网络协议定义
- `dma_network_client.c` - 客户端网络接口
- `dma_network_server.c` - 服务端网络和iocontrol接口
- `dma_server_main.c` - 服务器主程序
- `winusb_hook.cpp` - WinUSB API劫持DLL

## 部署步骤

### 1. 编译服务端程序
```bash
# 使用Visual Studio或gcc编译服务端
gcc -o dma_server.exe dma_server_main.c dma_network_server.c -lws2_32

# 或使用Visual Studio项目文件
```

### 2. 编译WinUSB劫持DLL
```bash
# 使用Visual Studio编译为DLL
cl /LD winusb_hook.cpp /Fe:winusb.dll /link ws2_32.lib

# 注意：需要设置正确的导出定义
```

### 3. 部署劫持DLL
1. 将原始`winusb.dll`重命名为`winusb_orig.dll`
2. 将编译的劫持DLL命名为`winusb.dll`
3. 放置在LeechCore.dll同目录下

### 4. 配置网络
1. 修改`winusb_hook.cpp`中的服务器IP地址
2. 确保网络连通性
3. 防火墙开放端口8888

## 运行步骤

### 服务端（有内核驱动的机器）
```bash
# 以管理员权限运行
dma_server.exe [端口号]
```

### 客户端（运行LeechCore的机器）
1. 确保劫持DLL已正确部署
2. 正常运行使用LeechCore的程序
3. USB操作会自动转换为网络操作

## 内核驱动接口说明

您需要实现内核驱动，支持以下IOCTL：

```c
#define IOCTL_READ_PHYSICAL_MEMORY  CTL_CODE(FILE_DEVICE_UNKNOWN, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_WRITE_PHYSICAL_MEMORY CTL_CODE(FILE_DEVICE_UNKNOWN, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)

typedef struct _PHYSICAL_MEMORY_REQUEST {
    QWORD PhysicalAddress;      // 物理内存地址
    DWORD Length;               // 数据长度
    UCHAR Data[1];              // 数据内容（可变长度）
} PHYSICAL_MEMORY_REQUEST;
```

### 驱动接口要求
- 设备名称：`\\\\.\\YourDmaDriver`
- 读取：输入地址和长度，返回数据
- 写入：输入地址、长度和数据，返回状态

## 调试说明

### 网络调试
- 使用Wireshark抓包检查网络通信
- 检查防火墙和端口开放情况
- 验证IP地址配置

### API劫持调试
- 使用Process Monitor检查DLL加载情况
- 在DLL中添加日志输出
- 确认PipeID映射正确

### 内存操作调试
- 检查内核驱动是否正确加载
- 验证IOCTL码定义
- 确认权限设置正确

## 性能优化

1. **网络优化**
   - 使用TCP_NODELAY减少延迟
   - 考虑批量传输
   - 实现连接池

2. **内存优化**
   - 重用缓冲区
   - 减少内存拷贝
   - 优化大块传输

3. **协议优化**
   - 添加压缩支持
   - 实现缓存机制
   - 优化包头大小

## 安全注意事项

1. **网络安全**
   - 使用私有网络
   - 考虑加密传输
   - 限制访问IP

2. **权限控制**
   - 服务端需要管理员权限
   - 限制内核驱动访问
   - 审计内存操作

## 故障排除

### 常见问题
1. **连接失败**：检查IP地址和端口配置
2. **劫持失败**：确认DLL部署位置正确
3. **权限错误**：确保管理员权限运行
4. **驱动错误**：检查内核驱动加载状态

### 日志输出
服务端会输出详细的操作日志，包括：
- 客户端连接状态
- 内存读写请求
- 错误信息

## 扩展功能

可以考虑添加：
- 多客户端支持
- 负载均衡
- 故障恢复
- 性能监控