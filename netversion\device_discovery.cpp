#include "dma_network_protocol.h"
#include <setupapi.h>
#include <iostream>
#include <vector>

#pragma comment(lib, "setupapi.lib")

// 枚举所有设备对象并查找我们的DMA设备
BOOL DmaDevice_FindRandomDevice(LPWSTR devicePath, DWORD pathSize)
{
    if (!devicePath || pathSize < MAX_PATH) {
        return FALSE;
    }

    // 清空输出缓冲区
    ZeroMemory(devicePath, pathSize * sizeof(WCHAR));

    // 获取所有设备接口
    HDEVINFO deviceInfoSet = SetupDiGetClassDevs(
        NULL,                       // 所有设备类
        NULL,                       // 不指定枚举器
        NULL,                       // 不指定父窗口
        DIGCF_PRESENT | DIGCF_ALLCLASSES | DIGCF_DEVICEINTERFACE
    );

    if (deviceInfoSet == INVALID_HANDLE_VALUE) {
        printf("[!] Failed to get device info set: %d\n", GetLastError());
        return FALSE;
    }

    SP_DEVICE_INTERFACE_DATA deviceInterfaceData;
    deviceInterfaceData.cbSize = sizeof(SP_DEVICE_INTERFACE_DATA);

    // 尝试通过枚举 \Device\ 目录的方式
    std::vector<std::wstring> candidateDevices;
    
    // 枚举所有可能的GUID设备名 - 使用更广泛的搜索范围
    for (int i = 0; i < 5000; i++) {
        WCHAR testPath[MAX_PATH];
        
        // 生成各种可能的GUID组合
        ULONG part1 = 0x10000000 + (i * 0x1000);
        ULONG part2 = 0x20000000 + (i * 0x1111);
        ULONG part3 = 0x30000000 + (i * 0x2222);
        ULONG part4 = 0x40000000 + (i * 0x3333);
        
        swprintf_s(testPath, MAX_PATH, L"\\\\.\\{%08x-%04x-%04x-%04x-%08x%04x}", 
            part1, 
            (USHORT)(part2 >> 16), 
            (USHORT)(part2 & 0xFFFF),
            (USHORT)(part3 >> 16),
            part4,
            (USHORT)(part3 & 0xFFFF));

        HANDLE hDevice = CreateFileW(
            testPath,
            GENERIC_READ | GENERIC_WRITE,
            FILE_SHARE_READ | FILE_SHARE_WRITE,
            NULL,
            OPEN_EXISTING,
            FILE_ATTRIBUTE_NORMAL,
            NULL
        );

        if (hDevice != INVALID_HANDLE_VALUE) {
            // 尝试识别这是否是我们的设备
            DWORD signature = 0;
            DWORD bytesReturned = 0;
            
            BOOL result = DeviceIoControl(
                hDevice,
                IOCTL_DMA_IDENTIFY,
                NULL, 0,
                &signature, sizeof(signature),
                &bytesReturned,
                NULL
            );

            if (result && signature == DMA_DEVICE_SIGNATURE) {
                printf("[+] Found DMA device: %ws\n", testPath);
                wcscpy_s(devicePath, pathSize, testPath);
                CloseHandle(hDevice);
                SetupDiDestroyDeviceInfoList(deviceInfoSet);
                return TRUE;
            }
            
            CloseHandle(hDevice);
        }
    }

    // 如果枚举失败，尝试通过NT对象目录枚举
    printf("[*] Trying NT object directory enumeration...\n");
    
    // 这里可以添加更复杂的NT对象目录枚举逻辑
    // 由于需要ntdll函数，这里暂时跳过

    SetupDiDestroyDeviceInfoList(deviceInfoSet);
    printf("[!] DMA device not found\n");
    return FALSE;
}

// 通过设备路径打开设备
HANDLE DmaDevice_OpenByPath(LPCWSTR devicePath)
{
    if (!devicePath) {
        return INVALID_HANDLE_VALUE;
    }

    HANDLE hDevice = CreateFileW(
        devicePath,
        GENERIC_READ | GENERIC_WRITE,
        FILE_SHARE_READ | FILE_SHARE_WRITE,
        NULL,
        OPEN_EXISTING,
        FILE_ATTRIBUTE_NORMAL,
        NULL
    );

    if (hDevice == INVALID_HANDLE_VALUE) {
        printf("[!] Failed to open device %ws: %d\n", devicePath, GetLastError());
        return INVALID_HANDLE_VALUE;
    }

    // 验证这是我们的设备
    DWORD signature = 0;
    DWORD bytesReturned = 0;
    
    BOOL result = DeviceIoControl(
        hDevice,
        IOCTL_DMA_IDENTIFY,
        NULL, 0,
        &signature, sizeof(signature),
        &bytesReturned,
        NULL
    );

    if (!result || signature != DMA_DEVICE_SIGNATURE) {
        printf("[!] Device signature verification failed\n");
        CloseHandle(hDevice);
        return INVALID_HANDLE_VALUE;
    }

    printf("[+] Successfully opened and verified DMA device\n");
    return hDevice;
}

// 改进的设备发现 - 使用NT对象管理器API
BOOL DmaDevice_FindRandomDeviceAdvanced(LPWSTR devicePath, DWORD pathSize)
{
    // 这个版本使用更高级的NT API来枚举对象目录
    // 需要链接 ntdll.lib 和相关头文件
    
    typedef struct _UNICODE_STRING {
        USHORT Length;
        USHORT MaximumLength;
        PWSTR  Buffer;
    } UNICODE_STRING, *PUNICODE_STRING;

    typedef struct _OBJECT_ATTRIBUTES {
        ULONG Length;
        HANDLE RootDirectory;
        PUNICODE_STRING ObjectName;
        ULONG Attributes;
        PVOID SecurityDescriptor;
        PVOID SecurityQualityOfService;
    } OBJECT_ATTRIBUTES, *POBJECT_ATTRIBUTES;

    // 这里需要动态加载ntdll函数
    // 由于复杂性，这里先返回简单版本的结果
    return DmaDevice_FindRandomDevice(devicePath, pathSize);
}