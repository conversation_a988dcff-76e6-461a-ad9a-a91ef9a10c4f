# DMA网络替代方案 - Visual Studio 2022项目

## 项目结构

```
netversion/
├── DmaNetworkSolution.sln          # VS2022解决方案文件
├── DmaNetworkServer/               # 服务端项目
│   └── DmaNetworkServer.vcxproj    
├── WinUsbHook/                     # WinUSB劫持DLL项目
│   ├── WinUsbHook.vcxproj
│   ├── winusb.def                  # DLL导出定义
│   └── dma_network_client.c        # 客户端网络代码
├── TestDmaNetwork/                 # 测试项目
│   └── TestDmaNetwork.vcxproj
├── dma_network_protocol.h          # 协议定义
├── dma_network_server.c            # 服务端实现
├── dma_server_main.c               # 服务端主程序
├── winusb_hook.cpp                 # WinUSB Hook实现
└── test_dma_network.c              # 测试程序
```

## 使用Visual Studio 2022

### 1. 打开项目
- 双击 `DmaNetworkSolution.sln` 打开解决方案
- VS2022会自动加载所有三个项目

### 2. 编译项目

#### 编译顺序：
1. **DmaNetworkServer** - 服务端程序
2. **WinUsbHook** - WinUSB劫持DLL（输出为winusb.dll）
3. **TestDmaNetwork** - 测试程序

#### 构建配置：
- **Debug/Release**: 根据需要选择
- **x64**: 推荐使用64位版本（与LeechCore保持一致）
- **x86**: 如果LeechCore是32位版本

### 3. 输出文件
所有编译输出位于：
- `bin/x64/Debug/` 或 `bin/x64/Release/`
- `bin/Win32/Debug/` 或 `bin/Win32/Release/`

编译后生成：
- `DmaNetworkServer.exe` - 服务端程序
- `winusb.dll` - WinUSB劫持DLL
- `TestDmaNetwork.exe` - 测试程序

### 4. 项目配置说明

#### DmaNetworkServer
- **类型**: 控制台应用程序
- **依赖**: ws2_32.lib
- **包含**: 服务端网络代码和iocontrol接口

#### WinUsbHook
- **类型**: 动态链接库(DLL)
- **输出名**: winusb.dll
- **依赖**: ws2_32.lib, winusb.lib
- **导出**: 通过winusb.def定义的WinUSB API
- **包含**: API劫持和网络客户端代码

#### TestDmaNetwork
- **类型**: 控制台应用程序
- **依赖**: ws2_32.lib
- **用途**: 测试网络协议通信

## 部署步骤

### 1. 编译所有项目
在VS2022中：
- 右键解决方案 → "生成解决方案"
- 或按 `Ctrl+Shift+B`

### 2. 部署服务端
1. 将 `DmaNetworkServer.exe` 复制到有内核驱动的机器
2. 以管理员权限运行
3. 确保防火墙开放8888端口

### 3. 部署客户端劫持DLL
1. 备份原始winusb.dll：
   - 找到LeechCore.dll所在目录
   - 将系统的winusb.dll复制到该目录并重命名为winusb_orig.dll
2. 将编译的winusb.dll复制到LeechCore.dll同目录
3. 修改winusb_hook.cpp中的服务器IP地址（第21行）

### 4. 测试连接
1. 先启动服务端程序
2. 运行TestDmaNetwork.exe测试连接
3. 正常使用LeechCore程序

## 调试技巧

### 1. VS2022调试
- 可以直接在VS中调试服务端程序
- 设置断点查看网络通信过程
- 使用输出窗口查看调试信息

### 2. DLL调试
- 在winusb_hook.cpp中添加OutputDebugString调试输出
- 使用DebugView工具查看调试信息
- 确认DLL是否被正确加载

### 3. 网络调试
- 使用Wireshark抓包分析
- 检查端口占用：`netstat -an | findstr 8888`
- 测试网络连通性

## 常见问题解决

### 1. 编译错误
- 确保安装了Windows 10 SDK
- 检查包含目录和库目录设置
- 确认所有源文件都在项目中

### 2. 链接错误
- 检查ws2_32.lib和winusb.lib是否正确链接
- 确认DEF文件路径正确
- 检查导出函数声明

### 3. 运行时错误
- 确认服务端有管理员权限
- 检查内核驱动是否正确加载
- 验证网络连接和防火墙设置

## 性能优化建议

1. **Release版本**: 生产环境使用Release配置
2. **编译优化**: 启用全程序优化
3. **网络优化**: 考虑添加TCP_NODELAY选项
4. **内存管理**: 检查内存泄漏，使用Application Verifier

## 扩展开发

可以基于现有项目扩展：
- 添加更多设备支持
- 实现加密传输
- 添加性能监控
- 支持多客户端连接