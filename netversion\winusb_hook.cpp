// winusb_hook.cpp - WinUSB API劫持DLL
// 简化版本，只劫持关键的读写函数

#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>

// 手动定义需要的WinUSB类型，避免包含winusb.h
typedef PVOID WINUSB_INTERFACE_HANDLE, *PWINUSB_INTERFACE_HANDLE;

// WinUSB管道策略常量
#define AUTO_CLEAR_STALL        0x02
#define PIPE_TRANSFER_TIMEOUT   0x03
#define IGNORE_SHORT_PACKETS    0x04
#define ALLOW_PARTIAL_READS     0x05

extern "C" {
#include "dma_network_protocol.h"
}

// FPGA协议网络客户端函数声明
extern "C" {
    BOOL NetworkSend(UCHAR PipeID, PUCHAR Buffer, <PERSON><PERSON><PERSON><PERSON> BufferLength, PULONG LengthTransferred);
    BOOL NetworkReceive(UCHAR PipeID, PUC<PERSON><PERSON> Buffer, <PERSON><PERSON><PERSON><PERSON> BufferLength, <PERSON><PERSON><PERSON><PERSON>G LengthTransferred);
}

// 原始WinUSB函数指针类型
typedef BOOL (WINAPI *pfnWinUsb_Initialize)(HANDLE, PWINUSB_INTERFACE_HANDLE);
typedef BOOL (WINAPI *pfnWinUsb_Free)(WINUSB_INTERFACE_HANDLE);
typedef BOOL (WINAPI *pfnWinUsb_WritePipe)(WINUSB_INTERFACE_HANDLE, UCHAR, PUCHAR, ULONG, PULONG, LPOVERLAPPED);
typedef BOOL (WINAPI *pfnWinUsb_ReadPipe)(WINUSB_INTERFACE_HANDLE, UCHAR, PUCHAR, ULONG, PULONG, LPOVERLAPPED);
typedef BOOL (WINAPI *pfnWinUsb_SetPipePolicy)(WINUSB_INTERFACE_HANDLE, UCHAR, ULONG, ULONG, PVOID);

static HMODULE g_hOriginalDLL = NULL;
static pfnWinUsb_Initialize Original_WinUsb_Initialize = NULL;
static pfnWinUsb_Free Original_WinUsb_Free = NULL;
static pfnWinUsb_WritePipe Original_WinUsb_WritePipe = NULL;
static pfnWinUsb_ReadPipe Original_WinUsb_ReadPipe = NULL;
static pfnWinUsb_SetPipePolicy Original_WinUsb_SetPipePolicy = NULL;

// 配置
static const char* g_serverIP = "*************";  // 修改为您的服务器IP

// 加载原始DLL
BOOL LoadOriginalDLL() {
    if (g_hOriginalDLL) return TRUE;
    
    char systemPath[MAX_PATH];
    GetSystemDirectoryA(systemPath, sizeof(systemPath));
    strcat_s(systemPath, "\\winusb.dll");
    
    g_hOriginalDLL = LoadLibraryA(systemPath);
    if (!g_hOriginalDLL) {
        return FALSE;
    }
    
    // 获取原始函数地址
    Original_WinUsb_Initialize = (pfnWinUsb_Initialize)GetProcAddress(g_hOriginalDLL, "WinUsb_Initialize");
    Original_WinUsb_Free = (pfnWinUsb_Free)GetProcAddress(g_hOriginalDLL, "WinUsb_Free");
    Original_WinUsb_WritePipe = (pfnWinUsb_WritePipe)GetProcAddress(g_hOriginalDLL, "WinUsb_WritePipe");
    Original_WinUsb_ReadPipe = (pfnWinUsb_ReadPipe)GetProcAddress(g_hOriginalDLL, "WinUsb_ReadPipe");
    Original_WinUsb_SetPipePolicy = (pfnWinUsb_SetPipePolicy)GetProcAddress(g_hOriginalDLL, "WinUsb_SetPipePolicy");
    
    return (Original_WinUsb_Initialize && Original_WinUsb_Free && 
            Original_WinUsb_WritePipe && Original_WinUsb_ReadPipe && 
            Original_WinUsb_SetPipePolicy);
}

// DLL入口点
BOOL APIENTRY DllMain(HMODULE hModule, DWORD dwReason, LPVOID lpReserved) {
    switch (dwReason) {
        case DLL_PROCESS_ATTACH:
            DisableThreadLibraryCalls(hModule);
            if (!LoadOriginalDLL()) {
                return FALSE;
            }
            break;
            
        case DLL_PROCESS_DETACH:
            if (g_hOriginalDLL) {
                FreeLibrary(g_hOriginalDLL);
            }
            break;
    }
    return TRUE;
}

// 导出函数
extern "C" {

__declspec(dllexport) BOOL WINAPI WinUsb_Initialize(
    HANDLE DeviceHandle,
    PWINUSB_INTERFACE_HANDLE InterfaceHandle)
{
    return Original_WinUsb_Initialize ? Original_WinUsb_Initialize(DeviceHandle, InterfaceHandle) : FALSE;
}

__declspec(dllexport) BOOL WINAPI WinUsb_Free(WINUSB_INTERFACE_HANDLE InterfaceHandle) {
    return Original_WinUsb_Free ? Original_WinUsb_Free(InterfaceHandle) : FALSE;
}

// 关键劫持函数 - 写入操作 (LeechCore发送FPGA协议到DMA板)
__declspec(dllexport) BOOL WINAPI WinUsb_WritePipe(
    WINUSB_INTERFACE_HANDLE InterfaceHandle,
    UCHAR PipeID,
    PUCHAR Buffer,
    ULONG BufferLength,
    PULONG LengthTransferred,
    LPOVERLAPPED Overlapped)
{
    // 检查是否是LeechCore的TX管道
    if (PipeID == 0x02) {  // LeechCore使用0x02管道发送
        // 转发原始FPGA协议数据到网络服务器
        return NetworkSend(PipeID, Buffer, BufferLength, LengthTransferred);
    }
    
    // 其他管道转发给原始函数
    return Original_WinUsb_WritePipe ? 
           Original_WinUsb_WritePipe(InterfaceHandle, PipeID, Buffer, BufferLength, LengthTransferred, Overlapped) : FALSE;
}

// 关键劫持函数 - 读取操作 (LeechCore接收FPGA协议响应)
__declspec(dllexport) BOOL WINAPI WinUsb_ReadPipe(
    WINUSB_INTERFACE_HANDLE InterfaceHandle,
    UCHAR PipeID,
    PUCHAR Buffer,
    ULONG BufferLength,
    PULONG LengthTransferred,
    LPOVERLAPPED Overlapped)
{
    // 检查是否是LeechCore的RX管道
    if (PipeID == 0x82) {  // LeechCore使用0x82管道接收
        // 从网络服务器接收FPGA协议响应数据
        return NetworkReceive(PipeID, Buffer, BufferLength, LengthTransferred);
    }
    
    // 其他管道转发给原始函数
    return Original_WinUsb_ReadPipe ? 
           Original_WinUsb_ReadPipe(InterfaceHandle, PipeID, Buffer, BufferLength, LengthTransferred, Overlapped) : FALSE;
}

// WinUSB管道策略设置函数
__declspec(dllexport) BOOL WINAPI WinUsb_SetPipePolicy(
    WINUSB_INTERFACE_HANDLE InterfaceHandle,
    UCHAR PipeID,
    ULONG PolicyType,
    ULONG ValueLength,
    PVOID Value)
{
    // 转发给原始函数 - 我们不需要劫持管道策略设置
    return Original_WinUsb_SetPipePolicy ? 
           Original_WinUsb_SetPipePolicy(InterfaceHandle, PipeID, PolicyType, ValueLength, Value) : FALSE;
}

} // extern "C"