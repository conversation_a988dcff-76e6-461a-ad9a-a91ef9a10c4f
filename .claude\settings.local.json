{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(move \"C:\\Users\\<USER>\\Desktop\\请勿下载到游戏机\\副机\\newleechcore\\dma_network_protocol.h\" \"C:\\Users\\<USER>\\Desktop\\请勿下载到游戏机\\副机\\newleechcore\\netversion\"\")", "Bash(move \"C:\\Users\\<USER>\\Desktop\\请勿下载到游戏机\\副机\\newleechcore\\dma_network_client.c\" \"C:\\Users\\<USER>\\Desktop\\请勿下载到游戏机\\副机\\newleechcore\\netversion\"\")", "<PERSON><PERSON>(move:*)", "Bash(move dma_network_client.c netversion )", "<PERSON><PERSON>(mv:*)", "Bash(msbuild DmaNetworkSolution.sln /p:Configuration=Release /p:Platform=x64)", "Bash(cd \"C:\\Users\\<USER>\\Desktop\\请勿下载到游戏机\\副机\\newleechcore\\netversion\")", "Bash(\"C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\MSBuild.exe\" DmaNetworkSolution.sln /p:Configuration=Release /p:Platform=x64)", "Bash(find:*)", "Bash(ls -la)", "Bash(cd \"C:\\Users\\<USER>\\Desktop\\请勿下载到游戏机\\副机\\newleechcore\\ReadPhys-master\")", "Bash(\"C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\MSBuild.exe\" ReadPhys.sln /p:Configuration=Release /p:Platform=x64)", "Bash(ls -la \"/c/Program Files\")", "Bash(ls -la \"/c/Program Files/Microsoft Visual Studio\")", "Bash(ls -la \"/c/Program Files/MSBuild\")", "Bash(\"/c/Program Files/MSBuild/Microsoft/VisualStudio/v17.0/MSBuild.exe\" ReadPhys.sln /p:Configuration=Release /p:Platform=x64)", "Bash(ls:*)", "Bash(rm:*)", "<PERSON><PERSON>(true)", "Bash(msbuild ReadPhys.sln /p:Configuration=Release /p:Platform=x64)", "Bash(cd \"C:\\Users\\<USER>\\Desktop\\请勿下载到游戏机\\副机\\newleechcore\\ReadPhys-master\\x64\")", "<PERSON><PERSON>(objdump -p ReadPhys.sys)", "Bash(cd \"C:\\Users\\<USER>\\Desktop\\请勿下载到游戏机\\副机\\newleechcore\\ReadPhys-master\\x64\\Release\")", "Bash(cl:*)", "Bash(g++:*)", "Bash(del x64Release*.* /q)"], "deny": []}}