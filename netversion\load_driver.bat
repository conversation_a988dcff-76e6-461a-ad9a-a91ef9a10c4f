@echo off
echo DMA Driver Testing Script
echo ========================

echo [*] Stopping any existing ReadPhys service...
sc stop ReadPhys >nul 2>&1
sc delete ReadPhys >nul 2>&1

echo [*] Installing ReadPhys driver...
set DRIVER_PATH=%~dp0..\ReadPhys-master\x64\Release\ReadPhys.sys

if not exist "%DRIVER_PATH%" (
    echo [!] Driver not found at: %DRIVER_PATH%
    echo [!] Please compile the driver first
    pause
    exit /b 1
)

sc create ReadPhys type= kernel binPath= "%DRIVER_PATH%" start= demand
if %errorlevel% neq 0 (
    echo [!] Failed to create service
    pause
    exit /b 1
)

echo [*] Starting ReadPhys driver...
sc start ReadPhys
if %errorlevel% neq 0 (
    echo [!] Failed to start driver
    echo [*] Check if you're running as Administrator
    echo [*] Check driver signing requirements
    pause
    exit /b 1
)

echo [+] Driver loaded successfully!
echo [*] Driver will use random GUID device name for anti-detection

echo.
echo [*] You can now test device discovery with:
echo     test_improved_discovery.exe
echo.
echo [*] To unload driver later, run:
echo     sc stop ReadPhys
echo     sc delete ReadPhys

pause