#include <windows.h>
#include <iostream>
#include <iomanip>
#include "../ReadPhys/DeviceNameGenerator.h"

// 驱动IOCTL定义 - 与内核驱动保持一致
#define IOCTL_DMA_READ_MEMORY   CTL_CODE(FILE_DEVICE_UNKNOWN, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_DMA_WRITE_MEMORY  CTL_CODE(FILE_DEVICE_UNKNOWN, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_DMA_IDENTIFY      CTL_CODE(FILE_DEVICE_UNKNOWN, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS)

// 设备识别标识符
#define DMA_DEVICE_SIGNATURE    0x444D4144  // "DMAD"

// 驱动请求结构
typedef struct _DMA_REQUEST {
    DWORD Command;      // DMA_CMD_read 或 DMA_CMD_WRITE
    ULONG64 Address;    // 物理内存地址
    DWORD Length;       // 数据长度
    DWORD Reserved;     // 保留字段
} DMA_REQUEST, *PDMA_REQUEST;

int main()
{
    std::wcout << L"ReadPhys R3 测试客户端 (基于CPU ID)" << std::endl;
    std::wcout << L"====================================" << std::endl;

    // 显示CPU ID信息
    ULONG cpuId = DeviceNameGenerator::GetCpuId();
    std::cout << "CPU ID: 0x" << std::hex << std::uppercase << cpuId << std::dec << std::endl;
    std::cout << std::endl;

    // 使用基于CPU ID的算法生成设备名称
    std::wstring deviceName = DeviceNameGenerator::GenerateDeviceName();
    std::wstring kernelDeviceName = DeviceNameGenerator::GenerateKernelDeviceName();

    std::wcout << L"计算出的设备名称: " << deviceName << std::endl;
    std::wcout << L"内核设备名称: " << kernelDeviceName << std::endl;
    std::wcout << L"注意: 设备名称基于当前系统的CPU ID生成" << std::endl;
    std::wcout << std::endl;

    // 尝试打开设备
    HANDLE hDevice = CreateFileW(
        deviceName.c_str(),
        GENERIC_READ | GENERIC_WRITE,
        0,
        NULL,
        OPEN_EXISTING,
        FILE_ATTRIBUTE_NORMAL,
        NULL
    );

    if (hDevice == INVALID_HANDLE_VALUE) {
        DWORD error = GetLastError();
        std::wcout << L"无法打开设备，错误代码: " << error << std::endl;
        
        if (error == ERROR_FILE_NOT_FOUND) {
            std::wcout << L"设备未找到，请确保驱动已加载。" << std::endl;
        }
        return 1;
    }

    std::wcout << L"成功打开设备！" << std::endl;

    // 测试设备识别
    DWORD signature = 0;
    DWORD bytesReturned = 0;
    
    BOOL result = DeviceIoControl(
        hDevice,
        IOCTL_DMA_IDENTIFY,
        NULL,
        0,
        &signature,
        sizeof(signature),
        &bytesReturned,
        NULL
    );

    if (result && bytesReturned == sizeof(DWORD)) {
        std::wcout << L"设备识别成功！" << std::endl;
        std::wcout << L"设备签名: 0x" << std::hex << std::uppercase << signature << std::endl;
        
        if (signature == DMA_DEVICE_SIGNATURE) {
            std::wcout << L"设备签名验证通过！" << std::endl;
        } else {
            std::wcout << L"设备签名不匹配！" << std::endl;
        }
    } else {
        std::wcout << L"设备识别失败，错误代码: " << GetLastError() << std::endl;
    }

    // 测试读取内存（示例）
    std::wcout << std::endl << L"测试内存读取..." << std::endl;
    
    DMA_REQUEST request = {0};
    request.Command = 1; // DMA_CMD_read
    request.Address = 0x1000; // 测试地址
    request.Length = 16; // 读取16字节
    
    BYTE buffer[16] = {0};
    
    result = DeviceIoControl(
        hDevice,
        IOCTL_DMA_READ_MEMORY,
        &request,
        sizeof(request),
        buffer,
        sizeof(buffer),
        &bytesReturned,
        NULL
    );

    if (result) {
        std::wcout << L"内存读取成功，读取了 " << bytesReturned << L" 字节:" << std::endl;
        for (DWORD i = 0; i < bytesReturned; i++) {
            std::wcout << std::hex << std::setw(2) << std::setfill(L'0') << buffer[i] << L" ";
        }
        std::wcout << std::endl;
    } else {
        std::wcout << L"内存读取失败，错误代码: " << GetLastError() << std::endl;
    }

    CloseHandle(hDevice);
    std::wcout << std::endl << L"测试完成。" << std::endl;
    
    return 0;
}
