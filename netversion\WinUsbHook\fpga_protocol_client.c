#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include "../dma_network_protocol.h"

#pragma comment(lib, "ws2_32.lib")

#define DMA_SERVER_IP   "*************"  // Replace with actual server IP
#define DMA_SERVER_PORT 8888

// LeechCore FPGA protocol constants
#define FPGA_TLP_MAGIC          0x77000000    // TX TLP
#define FPGA_TLP_VALID_LAST     0x77040000    // TX TLP VALID LAST
#define FPGA_LOOPBACK_TX        0x77020000    // LOOPBACK TX
#define FPGA_LOOPBACK_DATA      0xffeeddcc

// Network connection for raw protocol forwarding
static BOOL NetworkForwardRawProtocol(UCHAR PipeID, PUCHAR Buffer, ULON<PERSON> BufferLength, PULONG LengthTransferred)
{
    SOCKET sock;
    int result;
    
    *LengthTransferred = 0;
    
    // Create socket for each request (simpler connection management)
    sock = socket(AF_INET, SOCK_STREAM, 0);
    if (sock == INVALID_SOCKET) {
        return FALSE;
    }
    
    struct sockaddr_in server_addr;
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(DMA_SERVER_PORT);
    inet_pton(AF_INET, DMA_SERVER_IP, &server_addr.sin_addr);
    
    if (connect(sock, (struct sockaddr*)&server_addr, sizeof(server_addr)) == SOCKET_ERROR) {
        closesocket(sock);
        return FALSE;
    }
    
    // Send pipe ID first
    result = send(sock, (char*)&PipeID, 1, 0);
    if (result != 1) {
        closesocket(sock);
        return FALSE;
    }
    
    // Send buffer length
    result = send(sock, (char*)&BufferLength, sizeof(BufferLength), 0);
    if (result != sizeof(BufferLength)) {
        closesocket(sock);
        return FALSE;
    }
    
    // Send the raw FPGA protocol data
    result = send(sock, (char*)Buffer, BufferLength, 0);
    if (result == (int)BufferLength) {
        *LengthTransferred = BufferLength;
    }
    
    closesocket(sock);
    return (*LengthTransferred == BufferLength);
}

// WinUSB API hook implementation for sending FPGA protocol data
BOOL NetworkSend(UCHAR PipeID, PUCHAR Buffer, ULONG BufferLength, PULONG LengthTransferred) 
{
    // Validate pipe ID (LeechCore uses 0x02 for TX)
    if (PipeID != 0x02) {
        return FALSE;
    }
    
    return NetworkForwardRawProtocol(PipeID, Buffer, BufferLength, LengthTransferred);
}

// WinUSB API hook implementation for receiving FPGA protocol data  
BOOL NetworkReceive(UCHAR PipeID, PUCHAR Buffer, ULONG BufferLength, PULONG LengthTransferred)
{
    SOCKET sock;
    int result;
    
    *LengthTransferred = 0;
    
    // Validate pipe ID (LeechCore uses 0x82 for RX)
    if (PipeID != 0x82) {
        return FALSE;
    }
    
    // Create socket
    sock = socket(AF_INET, SOCK_STREAM, 0);
    if (sock == INVALID_SOCKET) {
        return FALSE;
    }
    
    struct sockaddr_in server_addr;
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(DMA_SERVER_PORT);
    inet_pton(AF_INET, DMA_SERVER_IP, &server_addr.sin_addr);
    
    if (connect(sock, (struct sockaddr*)&server_addr, sizeof(server_addr)) == SOCKET_ERROR) {
        closesocket(sock);
        return FALSE;
    }
    
    // Send pipe ID to indicate this is a read request
    result = send(sock, (char*)&PipeID, 1, 0);
    if (result != 1) {
        closesocket(sock);
        return FALSE;
    }
    
    // Send buffer length
    result = send(sock, (char*)&BufferLength, sizeof(BufferLength), 0);
    if (result != sizeof(BufferLength)) {
        closesocket(sock);
        return FALSE;
    }
    
    // Receive the response data
    DWORD totalReceived = 0;
    while (totalReceived < BufferLength) {
        result = recv(sock, (char*)Buffer + totalReceived, BufferLength - totalReceived, 0);
        if (result <= 0) {
            break;
        }
        totalReceived += result;
    }
    
    *LengthTransferred = totalReceived;
    closesocket(sock);
    
    return (totalReceived > 0);
}