﻿#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include "dma_network_protocol.h"

#pragma comment(lib, "ws2_32.lib")

// ȫ�ֿͻ���״̬
static SOCKET g_clientSocket = INVALID_SOCKET;
static struct sockaddr_in g_serverAddr;
static BOOL g_initialized = FALSE;

// ��ʼ������ͻ���
BOOL DmaNetwork_Initialize(const char* serverIP, WORD serverPort) {
    WSADATA wsaData;
    
    if (g_initialized) {
        return TRUE;
    }
    
    // ��ʼ��Winsock
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        return FALSE;
    }
    
    // ����socket
    g_clientSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (g_clientSocket == INVALID_SOCKET) {
        WSACleanup();
        return FALSE;
    }
    
    // ���÷�������ַ
    memset(&g_serverAddr, 0, sizeof(g_serverAddr));
    g_serverAddr.sin_family = AF_INET;
    g_serverAddr.sin_port = htons(serverPort);
    inet_pton(AF_INET, serverIP, &g_serverAddr.sin_addr);
    
    // ���ӵ�������
    if (connect(g_clientSocket, (struct sockaddr*)&g_serverAddr, sizeof(g_serverAddr)) == SOCKET_ERROR) {
        closesocket(g_clientSocket);
        g_clientSocket = INVALID_SOCKET;
        WSACleanup();
        return FALSE;
    }
    
    g_initialized = TRUE;
    return TRUE;
}

// �������ݰ�
static BOOL SendPacket(PVOID data, DWORD length) {
    DWORD totalSent = 0;
    while (totalSent < length) {
        int sent = send(g_clientSocket, (char*)data + totalSent, length - totalSent, 0);
        if (sent == SOCKET_ERROR) {
            return FALSE;
        }
        totalSent += sent;
    }
    return TRUE;
}

// �������ݰ�
static BOOL ReceivePacket(PVOID data, DWORD length) {
    DWORD totalReceived = 0;
    while (totalReceived < length) {
        int received = recv(g_clientSocket, (char*)data + totalReceived, length - totalReceived, 0);
        if (received == SOCKET_ERROR || received == 0) {
            return FALSE;
        }
        totalReceived += received;
    }
    return TRUE;
}

// ��ȡ�ڴ�
BOOL DmaNetwork_ReadMemory(QWORD address, PVOID buffer, DWORD length, PDWORD bytesRead) {
    if (!g_initialized || length > DMA_MAX_TRANSFER_SIZE) {
        return FALSE;
    }
    
    // ������ȡ����
    DMA_PACKET_HEADER header;
    header.magic = DMA_MAGIC;
    header.command = DMA_CMD_READ;
    header.address = address;
    header.length = length;
    header.reserved = 0;
    
    // ��������
    if (!SendPacket(&header, sizeof(header))) {
        return FALSE;
    }
    
    // ������Ӧͷ
    DMA_RESPONSE response;
    if (!ReceivePacket(&response, sizeof(response))) {
        return FALSE;
    }
    
    // �����Ӧ
    if (response.magic != DMA_MAGIC || response.status != 0) {
        return FALSE;
    }
    
    // ��������
    if (response.length > 0 && response.length <= length) {
        if (!ReceivePacket(buffer, response.length)) {
            return FALSE;
        }
        if (bytesRead) {
            *bytesRead = response.length;
        }
    }
    
    return TRUE;
}

// д���ڴ�
BOOL DmaNetwork_WriteMemory(QWORD address, PVOID buffer, DWORD length, PDWORD bytesWritten) {
    if (!g_initialized || length > DMA_MAX_TRANSFER_SIZE) {
        return FALSE;
    }
    
    // ����д������
    DMA_PACKET_HEADER header;
    header.magic = DMA_MAGIC;
    header.command = DMA_CMD_WRITE;
    header.address = address;
    header.length = length;
    header.reserved = 0;
    
    // ��������ͷ
    if (!SendPacket(&header, sizeof(header))) {
        return FALSE;
    }
    
    // ��������
    if (length > 0) {
        if (!SendPacket(buffer, length)) {
            return FALSE;
        }
    }
    
    // ������Ӧ
    DMA_RESPONSE response;
    if (!ReceivePacket(&response, sizeof(response))) {
        return FALSE;
    }
    
    // �����Ӧ
    if (response.magic != DMA_MAGIC || response.status != 0) {
        return FALSE;
    }
    
    if (bytesWritten) {
        *bytesWritten = length;
    }
    
    return TRUE;
}

// ������Դ
VOID DmaNetwork_Cleanup() {
    if (g_clientSocket != INVALID_SOCKET) {
        closesocket(g_clientSocket);
        g_clientSocket = INVALID_SOCKET;
    }
    
    if (g_initialized) {
        WSACleanup();
        g_initialized = FALSE;
    }
}

// WinUSB API�ٳ�ʵ��
BOOL NetworkSend(UCHAR PipeID, PUCHAR Buffer, ULONG BufferLength, PULONG LengthTransferred) {
    // ����PipeID�жϲ������ͣ�����򻯴�����
    // ʵ��ʵ������Ҫ����LeechCore�ľ���Э��������
    
    // ��������д��������Buffer������ַ������
    if (BufferLength < 16) return FALSE;  // ��С����С���
    
    QWORD address = *(QWORD*)Buffer;
    DWORD length = BufferLength - 8;
    
    return DmaNetwork_WriteMemory(address, Buffer + 8, length, LengthTransferred);
}

BOOL NetworkReceive(UCHAR PipeID, PUCHAR Buffer, ULONG BufferLength, PULONG LengthTransferred) {
    // �������Ƕ�������Buffer����Ҫ��ȡ�ĵ�ַ
    if (BufferLength < 8) return FALSE;
    
    QWORD address = *(QWORD*)Buffer;
    DWORD length = BufferLength - 8;
    
    return DmaNetwork_ReadMemory(address, Buffer + 8, length, LengthTransferred);
}