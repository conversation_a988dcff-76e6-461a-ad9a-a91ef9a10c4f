#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <stdio.h>
#include <string.h>
#include "dma_network_protocol.h"

// 简单测试程序，用于验证网络协议
int main(int argc, char* argv[]) {
    const char* serverIP = "127.0.0.1";
    WORD serverPort = DMA_SERVER_PORT;
    
    printf("=== DMA网络协议测试程序 ===\\n");
    
    // 解析命令行参数
    if (argc > 1) {
        serverIP = argv[1];
    }
    if (argc > 2) {
        serverPort = (WORD)atoi(argv[2]);
    }
    
    printf("连接服务器: %s:%d\\n", serverIP, serverPort);
    
    // 初始化网络连接
    if (!DmaNetwork_Initialize(serverIP, serverPort)) {
        printf("连接服务器失败\\n");
        return 1;
    }
    
    printf("连接成功，开始测试...\\n\\n");
    
    // 测试1：读取内存
    printf("测试1: 读取内存\\n");
    UCHAR readBuffer[256];
    DWORD bytesRead = 0;
    
    if (DmaNetwork_ReadMemory(0x1000, readBuffer, 256, &bytesRead)) {
        printf("读取成功: 地址=0x1000, 长度=%d\\n", bytesRead);
        printf("前16字节数据: ");
        for (int i = 0; i < 16 && i < bytesRead; i++) {
            printf("%02X ", readBuffer[i]);
        }
        printf("\\n");
    } else {
        printf("读取失败\\n");
    }
    
    printf("\\n");
    
    // 测试2：写入内存
    printf("测试2: 写入内存\\n");
    UCHAR writeBuffer[64];
    memset(writeBuffer, 0xAA, sizeof(writeBuffer));
    DWORD bytesWritten = 0;
    
    if (DmaNetwork_WriteMemory(0x2000, writeBuffer, sizeof(writeBuffer), &bytesWritten)) {
        printf("写入成功: 地址=0x2000, 长度=%d\\n", bytesWritten);
    } else {
        printf("写入失败\\n");
    }
    
    printf("\\n");
    
    // 测试3：验证写入
    printf("测试3: 验证写入数据\\n");
    UCHAR verifyBuffer[64];
    DWORD bytesVerified = 0;
    
    if (DmaNetwork_ReadMemory(0x2000, verifyBuffer, sizeof(verifyBuffer), &bytesVerified)) {
        printf("验证读取成功: 长度=%d\\n", bytesVerified);
        
        BOOL match = TRUE;
        for (DWORD i = 0; i < bytesVerified && i < sizeof(writeBuffer); i++) {
            if (verifyBuffer[i] != writeBuffer[i]) {
                match = FALSE;
                break;
            }
        }
        
        if (match) {
            printf("数据校验成功！写入和读取数据一致\\n");
        } else {
            printf("数据校验失败！写入和读取数据不一致\\n");
        }
    } else {
        printf("验证读取失败\\n");
    }
    
    printf("\\n");
    
    // 测试4：大块数据传输
    printf("测试4: 大块数据传输 (1KB)\\n");
    PVOID bigBuffer = malloc(1024);
    if (bigBuffer) {
        memset(bigBuffer, 0x55, 1024);
        DWORD bigBytesWritten = 0;
        
        if (DmaNetwork_WriteMemory(0x10000, bigBuffer, 1024, &bigBytesWritten)) {
            printf("大块写入成功: 长度=%d\\n", bigBytesWritten);
            
            // 读取验证
            PVOID bigReadBuffer = malloc(1024);
            if (bigReadBuffer) {
                DWORD bigBytesRead = 0;
                if (DmaNetwork_ReadMemory(0x10000, bigReadBuffer, 1024, &bigBytesRead)) {
                    printf("大块读取成功: 长度=%d\\n", bigBytesRead);
                    
                    if (memcmp(bigBuffer, bigReadBuffer, min(bigBytesWritten, bigBytesRead)) == 0) {
                        printf("大块数据校验成功！\\n");
                    } else {
                        printf("大块数据校验失败！\\n");
                    }
                } else {
                    printf("大块读取失败\\n");
                }
                free(bigReadBuffer);
            }
        } else {
            printf("大块写入失败\\n");
        }
        free(bigBuffer);
    }
    
    // 清理连接
    DmaNetwork_Cleanup();
    
    printf("\\n测试完成\\n");
    return 0;
}